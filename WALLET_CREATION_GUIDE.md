# Wallet Creation Guide

This guide explains how to use the `WalletManager` class to create wallets for Bitcoin, Ethereum, Solana, and Sui blockchains.

## Features

- ✅ **Multi-blockchain support**: Create wallets for Bitcoin, Ethereum, Solana, and Sui from a single seed phrase
- ✅ **BIP44 HD wallet derivation**: Standard derivation paths for each blockchain
- ✅ **Secure encryption**: Private keys are encrypted using AES-256-GCM
- ✅ **Session management**: Configurable session duration with automatic expiry
- ✅ **Password-free operations**: Use session for signing without re-entering password
- ✅ **Multiple seed phrases**: Support for importing and managing multiple seed phrases
- ✅ **Multiple accounts**: Create multiple accounts from the same seed phrase
- ✅ **Chrome storage**: Persistent storage in browser extension

## Installation

The required dependencies are already added:

```bash
pnpm add @solana/web3.js @mysten/sui bip39 bitcoinjs-lib ecpair tiny-secp256k1
```

## Basic Usage

### 1. Initialize WalletManager

```typescript
import { WalletManager } from './src/types/wallet';

// Initialize with default 30-minute session
const walletManager = new WalletManager();

// Or initialize with custom session duration (in minutes)
const walletManager = new WalletManager(60); // 60-minute session
```

### 2. Generate and Import Seed Phrase

```typescript
// Generate a new seed phrase
const seedPhrase = await walletManager.generateSeedPhrase();
console.log('Generated seed phrase:', seedPhrase);

// Import the seed phrase with a password
const password = 'mySecurePassword123';
const seedPhraseId = await walletManager.importSeedPhrase(seedPhrase, password);
```

### 3. Create Wallet Account

```typescript
// Create wallet account for all 4 blockchains
const walletAccount = await walletManager.createWallet(
    0,                    // Seed phrase index (0 = first seed phrase)
    password,             // Password for encryption
    'My First Account',   // Account name
    0                     // Account index (0 = first account from this seed)
);

console.log('Wallet created!');
console.log('Bitcoin Address:', walletAccount.wallets.bitcoin.address);
console.log('Bitcoin Public Key:', walletAccount.wallets.bitcoin.publicKey);
console.log('Ethereum Address:', walletAccount.wallets.ethereum.address);
console.log('Ethereum Public Key:', walletAccount.wallets.ethereum.publicKey);
console.log('Solana Address:', walletAccount.wallets.solana.publicKey);
console.log('Sui Address:', walletAccount.wallets.sui.publicKey);
```

## Session Management

### Unlock Wallet for Session-Based Operations

```typescript
// Unlock wallet with password (creates session)
const unlocked = await walletManager.unlockWallet(password);
if (unlocked) {
    console.log('Wallet unlocked for session-based operations');
}

// Check session status
const sessionInfo = walletManager.getSessionInfo();
console.log('Session unlocked:', sessionInfo.isUnlocked);
console.log('Remaining minutes:', sessionInfo.remainingMinutes);
```

### Session-Based Operations (No Password Required)

```typescript
// Create wallet using session (no password needed)
const walletAccount = await walletManager.createWalletFromSession(
    0,                    // Seed phrase index
    'My Account',         // Account name
    0                     // Account index
);

// Get private keys using session
const btcPrivateKey = await walletManager.getPrivateKeyFromSession(0, 'bitcoin');
const ethPrivateKey = await walletManager.getPrivateKeyFromSession(0, 'ethereum');
const solPrivateKey = await walletManager.getPrivateKeyFromSession(0, 'solana');
const suiPrivateKey = await walletManager.getPrivateKeyFromSession(0, 'sui');

// Sign data using session
const btcSignature = await walletManager.signData(0, 'bitcoin', 'data to sign');
const ethSignature = await walletManager.signData(0, 'ethereum', 'data to sign');
```

### Session Management

```typescript
// Extend current session
walletManager.extendSession(30); // Add 30 more minutes

// Change session duration for future sessions
walletManager.setSessionDuration(60); // 60 minutes

// Get current session duration setting
const duration = walletManager.getSessionDuration(); // returns minutes

// Lock wallet manually
walletManager.lockWallet();

// Check if wallet is unlocked
const isUnlocked = walletManager.isWalletUnlocked();
```

### Fallback to Explicit Password

```typescript
// If session is expired/locked, you can still provide explicit password
const btcPrivateKey = await walletManager.getPrivateKeyFromSession(
    0,
    'bitcoin',
    'myPassword123' // Explicit password as fallback
);
const ethPrivateKey = await walletManager.getPrivateKeyFromSession(
    0,
    'ethereum',
    'myPassword123' // Explicit password as fallback
);
```

## Advanced Usage

### Account Management with IDs

Each account has a unique ID for easy management:

```typescript
// Create account and get ID
const account = await walletManager.createWallet(0, password, 'My Account', 0);
console.log('Account ID:', account.id);

// Get account by ID
const foundAccount = walletManager.getAccountById(account.id);

// Get private key by account ID
const privateKey = await walletManager.getPrivateKeyById(account.id, 'bitcoin');

// Update account name
await walletManager.updateAccountName(account.id, 'New Name', password);

// Remove account by ID
await walletManager.removeWalletAccountById(account.id, password);
```

### Import Account from Private Key

```typescript
// Import Bitcoin account from private key
const btcAccount = await walletManager.importAccountFromPrivateKey(
    'your_bitcoin_private_key',
    'bitcoin',
    'Imported Bitcoin Account',
    password
);

// Import Ethereum account from private key
const ethAccount = await walletManager.importAccountFromPrivateKey(
    '0x4c0883a69102937d6231471b5dbb6204fe5129617082792ae468d01a3f362318',
    'ethereum',
    'Imported Ethereum Account',
    password
);
```

### Import Account from Seed Phrase

```typescript
// Import account from existing or new seed phrase
const account = await walletManager.importAccountFromSeedPhrase(
    'your twelve word seed phrase here...',
    'Account Name',
    password,
    0 // derivation index
);
```

### Multiple Accounts from Same Seed

```typescript
// Create multiple accounts from the same seed phrase
const account1 = await walletManager.createWallet(0, password, 'Account 1', 0);
const account2 = await walletManager.createWallet(0, password, 'Account 2', 1);
const account3 = await walletManager.createWallet(0, password, 'Account 3', 2);
```

### Account Organization

```typescript
// Get accounts by source type
const seedAccounts = walletManager.getAccountsBySource('seed');
const privateKeyAccounts = walletManager.getAccountsBySource('privateKey');

// Get accounts by seed phrase
const seed1Accounts = walletManager.getAccountsBySeedPhrase(0);

// Get account index by ID
const accountIndex = walletManager.getAccountIndexById(account.id);
```

### Multiple Seed Phrases

```typescript
// Import first seed phrase
const seedPhrase1 = await walletManager.generateSeedPhrase();
await walletManager.importSeedPhrase(seedPhrase1, password);

// Import second seed phrase
const seedPhrase2 = await walletManager.generateSeedPhrase();
await walletManager.importSeedPhrase(seedPhrase2, password);

// Create accounts from different seed phrases
const accountFromSeed1 = await walletManager.createWallet(0, password, 'From Seed 1', 0);
const accountFromSeed2 = await walletManager.createWallet(1, password, 'From Seed 2', 0);
```

### Getting Private Keys

```typescript
// Get private key for specific blockchain (handle with care!)
const btcPrivateKey = await walletManager.getPrivateKey(0, 'bitcoin', password);
const ethPrivateKey = await walletManager.getPrivateKey(0, 'ethereum', password);
const solPrivateKey = await walletManager.getPrivateKey(0, 'solana', password);
const suiPrivateKey = await walletManager.getPrivateKey(0, 'sui', password);
```

### Getting Public Keys/Addresses

```typescript
// Get public keys without password
const btcPublicKey = walletManager.getPublicKey(0, 'bitcoin');
const ethPublicKey = walletManager.getPublicKey(0, 'ethereum');
const solAddress = walletManager.getPublicKey(0, 'solana');
const suiAddress = walletManager.getPublicKey(0, 'sui');

// Get wallet addresses
const btcAddress = walletManager.getWalletAddress(0, 'bitcoin');
const ethAddress = walletManager.getWalletAddress(0, 'ethereum');
const solAddress = walletManager.getWalletAddress(0, 'solana');
const suiAddress = walletManager.getWalletAddress(0, 'sui');
```

## Derivation Paths

The wallet uses standard BIP44 derivation paths:

- **Bitcoin**: `m/44'/0'/0'/0/{account_index}`
- **Ethereum**: `m/44'/60'/0'/0/{account_index}`
- **Solana**: `m/44'/501'/{account_index}'/0'`
- **Sui**: `m/44'/784'/0'/0'/{account_index}'`

## Security Features

1. **Password Protection**: All operations require password verification
2. **Encrypted Storage**: Private keys are encrypted using AES-256-GCM
3. **Secure Key Derivation**: Uses PBKDF2 with 100,000 iterations
4. **No Plain Text Storage**: Seed phrases and private keys are never stored in plain text

## Error Handling

The WalletManager includes comprehensive error handling:

```typescript
try {
    const walletAccount = await walletManager.createWallet(0, password, 'My Account', 0);
} catch (error) {
    if (error.message === 'Invalid password') {
        // Handle wrong password
    } else if (error.message === 'Invalid seed phrase index') {
        // Handle invalid seed phrase index
    } else {
        // Handle other errors
    }
}
```

## Events

The WalletManager emits events for important operations:

```typescript
walletManager.addEventListener('seedPhraseImported', (event) => {
    console.log('Seed phrase imported:', event.detail);
});

walletManager.addEventListener('walletCreated', (event) => {
    console.log('Wallet created:', event.detail);
    console.log('Bitcoin address:', event.detail.wallets.bitcoin.address);
});

walletManager.addEventListener('walletCreationError', (event) => {
    console.error('Wallet creation error:', event.detail.error);
});
```

## Example Files

Check out the example files:

- `src/examples/wallet-creation-example.ts` - Complete examples of wallet creation
- `src/examples/bitcoin-wallet-example.ts` - Bitcoin-specific examples
- `src/examples/session-management-example.ts` - Session management examples
- `src/examples/advanced-account-management.ts` - Advanced account management
- Run examples to see the wallet creation in action

## Best Practices

1. **Strong Passwords**: Use strong passwords for encryption
2. **Secure Storage**: Never log or expose private keys in production
3. **Error Handling**: Always wrap wallet operations in try-catch blocks
4. **Backup Seed Phrases**: Ensure users backup their seed phrases securely
5. **Validate Inputs**: Always validate user inputs before processing

## Troubleshooting

### Common Issues

1. **"Invalid password"**: Make sure you're using the correct password
2. **"Invalid seed phrase format"**: Ensure seed phrase has 12, 15, 18, 21, or 24 words
3. **"Seed phrase already imported"**: The seed phrase is already in the wallet
4. **"Invalid seed phrase index"**: Check that the seed phrase index exists

### Debug Mode

Enable debug logging to troubleshoot issues:

```typescript
// Check wallet state
console.log('Seed phrase count:', walletManager.getSeedPhraseCount());
console.log('Account count:', walletManager.getWalletAccounts().length);
console.log('Has wallet:', walletManager.isHasWallet());
```
