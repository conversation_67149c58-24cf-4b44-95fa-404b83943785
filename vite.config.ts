import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import { copyFileSync, mkdirSync } from 'fs'
import { resolve } from 'path'

export default defineConfig({
    plugins: [
        tailwindcss(),
        react(),
        {
            name: 'copy-files',
            writeBundle() {
                copyFileSync('src/manifest.json', 'dist/manifest.json')
            }
        }
    ],
    build: {
        rollupOptions: {
            input: {
                main: resolve(__dirname, 'src/html/main.html'),
                background: resolve(__dirname, 'src/background/background.ts'),
                contentScript: resolve(__dirname, 'src/background/content-script.ts'),
                injectedProviderBundle: resolve(__dirname, 'src/background/injected-provider-bundle.ts'),
            },
            output: {
                entryFileNames: '[name].js',
                chunkFileNames: (chunkInfo) => {
                    // Rename _commonjsHelpers to avoid Chrome extension error
                    if (chunkInfo.name === '_commonjsHelpers') {
                        return 'commonjs-helpers.js';
                    }
                    return '[name].js';
                },
                assetFileNames: '[name].[ext]',
                format: 'es'
            }
        },
        outDir: 'dist',
        emptyOutDir: true,
        target: 'es2022',
        minify: 'esbuild',
        sourcemap: false
    },
    define: {
        global: 'globalThis',
    },
    optimizeDeps: {
        include: ['buffer', 'process', 'ethers', '@solana/web3.js', '@mysten/sui', 'bip39']
    },
    resolve: {
        alias: {
            buffer: 'buffer',
            process: 'process',
        }
    }
})