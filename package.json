{"type": "module", "scripts": {"build": "vite build", "build:watch": "vite build --watch --mode development", "type-check": "tsc --noEmit"}, "dependencies": {"@mysten/sui": "^1.30.5", "@solana/web3.js": "^1.98.2", "@tailwindcss/vite": "^4.1.8", "bip39": "^3.1.0", "buffer": "^6.0.3", "ethers": "^6.14.3", "lucide-react": "^0.513.0", "motion": "^12.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.8", "vite": "^6.3.5", "zustand": "^5.0.5"}, "devDependencies": {"@types/chrome": "^0.0.326", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.1"}}