export interface Account {
    id: string; // Unique account ID
    name: string;
    source: 'seed' | 'privateKey'; // How the account was created
    fromSeedEncrypted?: string; // Only for seed-based accounts
    derivationIndex?: number; // Only for seed-based accounts
    seedPhraseIndex?: number; // Which seed phrase was used
    wallets: {
        bitcoin: {
            privateKeyEncrypted: string;
            publicKey: string;
            addresses: {
                legacy: string;      // P2PKH - Legacy format (1...)
                segwit: string;      // P2SH-P2WPKH - SegWit wrapped (3...)
                nativeSegwit: string; // P2WPKH - Native SegWit (bc1...)
            };
        },
        ethereum: {
            privateKeyEncrypted: string;
            publicKey: string;
            address: string; // Ethereum address derived from public key
        },
        solana: {
            privateKeyEncrypted: string;
            publicKey: string;
        },
        sui: {
            privateKeyEncrypted: string;
            publicKey: string;
        },
    }
}

export interface WalletSession {
    passwordEncrypted: string; // Encrypted password for session use
    salt: string;
    timestamp: number;
    expiresAt: number;
}

export interface WalletState {
    seedPhraseEncrypted: string[];
    accounts: Account[];
    passwordEncrypted: string;
    sessionSalt: string;
}

export interface WalletRuntimeState {
    session: WalletSession | null;
    isUnlocked: boolean;
}

export class WalletManager extends EventTarget {
    private state: WalletState;
    private runtimeState: WalletRuntimeState;
    private sessionDuration: number; // in milliseconds

    constructor(sessionDurationMinutes: number = 30) {
        super();
        this.state = {
            seedPhraseEncrypted: [],
            accounts: [],
            passwordEncrypted: "",
            sessionSalt: "",
        };

        this.runtimeState = {
            session: null,
            isUnlocked: false
        };

        this.sessionDuration = sessionDurationMinutes * 60 * 1000;
        this.initialize();
    }

    private async initialize() {
        try {
            const result = await chrome.storage.local.get(['walletState']);
            if (result.walletState) {
                this.state = { ...this.state, ...result.walletState };
            }
        } catch (error) {
            console.error('Failed to load wallet state:', error);
        }
    }

    // Helper method để tạo key từ password
    private async deriveKeyFromPassword(password: string, salt: Uint8Array): Promise<CryptoKey> {
        const passwordBuffer = new TextEncoder().encode(password);

        // Import password as key material
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            passwordBuffer,
            { name: 'PBKDF2' },
            false,
            ['deriveKey']
        );

        // Derive actual encryption key
        return crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 100000, // 100k iterations for security
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-GCM', length: 256 },
            false,
            ['encrypt', 'decrypt']
        );
    }

    // Helper method để encrypt data
    private async encryptData(data: string, password: string): Promise<string> {
        // Generate random salt and IV
        const salt = crypto.getRandomValues(new Uint8Array(16));
        const iv = crypto.getRandomValues(new Uint8Array(12));

        // Derive key from password
        const key = await this.deriveKeyFromPassword(password, salt);

        // Encrypt data
        const dataBuffer = new TextEncoder().encode(data);
        const encryptedBuffer = await crypto.subtle.encrypt(
            {
                name: 'AES-GCM',
                iv: iv
            },
            key,
            dataBuffer
        );

        // Combine salt + iv + encrypted data
        const combined = new Uint8Array(salt.length + iv.length + encryptedBuffer.byteLength);
        combined.set(salt, 0);
        combined.set(iv, salt.length);
        combined.set(new Uint8Array(encryptedBuffer), salt.length + iv.length);

        // Return as base64 string
        return btoa(String.fromCharCode(...combined));
    }

    // Helper method để encrypt data with specific salt
    private async encryptDataWithSalt(data: string, salt: Uint8Array): Promise<string> {
        const iv = crypto.getRandomValues(new Uint8Array(12));

        // Derive key from session salt
        const key = await this.deriveKeyFromPassword('session_key', salt);

        // Encrypt data
        const dataBuffer = new TextEncoder().encode(data);
        const encryptedBuffer = await crypto.subtle.encrypt(
            {
                name: 'AES-GCM',
                iv: iv
            },
            key,
            dataBuffer
        );

        // Combine iv + encrypted data (salt is stored separately)
        const combined = new Uint8Array(iv.length + encryptedBuffer.byteLength);
        combined.set(iv, 0);
        combined.set(new Uint8Array(encryptedBuffer), iv.length);

        // Return as base64 string
        return btoa(String.fromCharCode(...combined));
    }

    // Helper method để decrypt data with specific salt
    private async decryptDataWithSalt(encryptedData: string, salt: Uint8Array): Promise<string> {
        try {
            // Decode from base64
            const combined = new Uint8Array(
                atob(encryptedData).split('').map(char => char.charCodeAt(0))
            );

            // Extract iv and encrypted data
            const iv = combined.slice(0, 12);
            const encrypted = combined.slice(12);

            // Derive key from session salt
            const key = await this.deriveKeyFromPassword('session_key', salt);

            // Decrypt data
            const decryptedBuffer = await crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: iv
                },
                key,
                encrypted
            );

            return new TextDecoder().decode(decryptedBuffer);
        } catch (error) {
            throw new Error('Failed to decrypt session data.');
        }
    }

    // Helper method để hash password
    private async hashPassword(password: string): Promise<string> {
        const encoder = new TextEncoder();
        const data = encoder.encode(password);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // Validate seed phrase format
    private validateSeedPhrase(seedPhrase: string): boolean {
        const words = seedPhrase.trim().split(/\s+/);
        // Check if it's 12, 15, 18, 21, or 24 words
        return [12, 15, 18, 21, 24].includes(words.length);
    }

    async importSeedPhrase(seedPhrase: string, password: string): Promise<string> {
        try {
            // Validate inputs
            if (!seedPhrase || !password) {
                throw new Error('Seed phrase and password are required');
            }

            if (!this.validateSeedPhrase(seedPhrase)) {
                throw new Error('Invalid seed phrase format. Must be 12, 15, 18, 21, or 24 words');
            }

            if (password.length < 8) {
                throw new Error('Password must be at least 8 characters long');
            }

            // Check if this seed phrase already exists
            const seedPhraseHash = await this.hashPassword(seedPhrase);
            const existingSeedIndex = await this.findExistingSeedPhrase(seedPhrase, password);
            if (existingSeedIndex !== -1) {
                throw new Error('This seed phrase is already imported');
            }

            // Set password hash if this is the first seed phrase
            if (this.state.seedPhraseEncrypted.length === 0) {
                const passwordHash = await this.hashPassword(password);
                this.state.passwordEncrypted = passwordHash;

                // Generate session salt for this session
                const sessionSalt = crypto.getRandomValues(new Uint8Array(16));
                this.state.sessionSalt = btoa(String.fromCharCode(...sessionSalt));
            } else {
                // Verify password matches existing password
                const isValidPassword = await this.verifyPassword(password);
                if (!isValidPassword) {
                    throw new Error('Password does not match existing wallet password');
                }
            }

            // Encrypt the entire seed phrase as one string
            const encryptedSeedPhrase = await this.encryptData(seedPhrase, password);

            // Add to seed phrases array
            this.state.seedPhraseEncrypted.push(encryptedSeedPhrase);

            // Save to chrome storage
            await chrome.storage.local.set({ walletState: this.state });

            // Generate unique ID for this seed phrase
            const seedPhraseId = `seed_${Date.now()}_${this.state.seedPhraseEncrypted.length - 1}`;

            // Dispatch event
            this.dispatchEvent(new CustomEvent('seedPhraseImported', {
                detail: {
                    success: true,
                    seedPhraseId,
                    index: this.state.seedPhraseEncrypted.length - 1,
                    totalSeedPhrases: this.state.seedPhraseEncrypted.length
                }
            }));

            console.log(`Seed phrase imported successfully. Total: ${this.state.seedPhraseEncrypted.length}`);
            return seedPhraseId;

        } catch (error) {
            console.error('Failed to import seed phrase:', error);

            // Dispatch error event
            this.dispatchEvent(new CustomEvent('seedPhraseImportError', {
                detail: { error: error.message }
            }));

            throw error;
        }
    }

    async verifyPassword(password: string): Promise<boolean> {
        try {
            const passwordHash = await this.hashPassword(password);
            return passwordHash === this.state.passwordEncrypted;
        } catch (error) {
            console.error('Password verification failed:', error);
            return false;
        }
    }


    private async findExistingSeedPhrase(seedPhrase: string, password: string): Promise<number> {
        for (let i = 0; i < this.state.seedPhraseEncrypted.length; i++) {
            try {
                const decryptedSeed = await this.decryptData(this.state.seedPhraseEncrypted[i], password);
                if (decryptedSeed === seedPhrase) {
                    return i;
                }
            } catch (error) {
                // Continue checking other seed phrases
                continue;
            }
        }
        return -1;
    }


    private async decryptData(encryptedData: string, password: string): Promise<string> {
        try {
            // Decode from base64
            const combined = new Uint8Array(
                atob(encryptedData).split('').map(char => char.charCodeAt(0))
            );

            // Extract salt, iv, and encrypted data
            const salt = combined.slice(0, 16);
            const iv = combined.slice(16, 28);
            const encrypted = combined.slice(28);

            // Derive key from password
            const key = await this.deriveKeyFromPassword(password, salt);

            // Decrypt data
            const decryptedBuffer = await crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: iv
                },
                key,
                encrypted
            );

            return new TextDecoder().decode(decryptedBuffer);
        } catch (error) {
            throw new Error('Failed to decrypt data. Wrong password or corrupted data.');
        }
    }


    async getAllSeedPhrases(password: string): Promise<string[]> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        const seedPhrases: string[] = [];
        for (const encryptedSeed of this.state.seedPhraseEncrypted) {
            try {
                const decryptedSeed = await this.decryptData(encryptedSeed, password);
                seedPhrases.push(decryptedSeed);
            } catch (error) {
                console.error('Failed to decrypt seed phrase:', error);
                seedPhrases.push('[FAILED_TO_DECRYPT]');
            }
        }
        return seedPhrases;
    }

    async getSeedPhrase(index: number, password: string): Promise<string> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        if (index < 0 || index >= this.state.seedPhraseEncrypted.length) {
            throw new Error('Invalid seed phrase index');
        }

        return await this.decryptData(this.state.seedPhraseEncrypted[index], password);
    }


    async removeSeedPhrase(index: number, password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        if (index < 0 || index >= this.state.seedPhraseEncrypted.length) {
            throw new Error('Invalid seed phrase index');
        }

        // Remove the seed phrase
        this.state.seedPhraseEncrypted.splice(index, 1);

        // If this was the last seed phrase, clear password too
        if (this.state.seedPhraseEncrypted.length === 0) {
            this.state.passwordEncrypted = "";
            this.state.sessionSalt = "";
        }

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('seedPhraseRemoved', {
            detail: {
                removedIndex: index,
                totalSeedPhrases: this.state.seedPhraseEncrypted.length
            }
        }));
    }


    getSeedPhraseCount(): number {
        return this.state.seedPhraseEncrypted.length;
    }

    isHasWallet(): boolean {
        return this.state.seedPhraseEncrypted.length > 0 &&
            this.state.passwordEncrypted.length > 0;
    }

    // Session Management Methods

    // Unlock wallet with password and create session
    async unlockWallet(password: string): Promise<boolean> {
        try {
            const isValidPassword = await this.verifyPassword(password);
            if (!isValidPassword) {
                return false;
            }

            // Create session with encrypted password
            const sessionSalt = crypto.getRandomValues(new Uint8Array(16));
            const saltString = btoa(String.fromCharCode(...sessionSalt));

            // Encrypt password for session storage (using session salt)
            const passwordEncrypted = await this.encryptDataWithSalt(password, sessionSalt);
            const now = Date.now();

            this.runtimeState.session = {
                passwordEncrypted,
                salt: saltString,
                timestamp: now,
                expiresAt: now + this.sessionDuration
            };

            this.runtimeState.isUnlocked = true;

            // Dispatch unlock event
            this.dispatchEvent(new CustomEvent('walletUnlocked', {
                detail: {
                    timestamp: now,
                    expiresAt: this.runtimeState.session.expiresAt,
                    durationMinutes: this.sessionDuration / (60 * 1000)
                }
            }));

            console.log(`Wallet unlocked for ${this.sessionDuration / (60 * 1000)} minutes`);
            return true;

        } catch (error) {
            console.error('Failed to unlock wallet:', error);
            return false;
        }
    }

    // Lock wallet and clear session
    lockWallet(): void {
        this.runtimeState.session = null;
        this.runtimeState.isUnlocked = false;

        // Dispatch lock event
        this.dispatchEvent(new CustomEvent('walletLocked', {
            detail: { timestamp: Date.now() }
        }));

        console.log('Wallet locked');
    }

    // Check if wallet is unlocked and session is valid
    isWalletUnlocked(): boolean {
        if (!this.runtimeState.isUnlocked || !this.runtimeState.session) {
            return false;
        }

        // Check if session has expired
        if (Date.now() > this.runtimeState.session.expiresAt) {
            this.lockWallet();
            return false;
        }

        return true;
    }

    // Extend session duration
    extendSession(additionalMinutes: number = 30): boolean {
        if (!this.isWalletUnlocked()) {
            return false;
        }

        const additionalTime = additionalMinutes * 60 * 1000;
        this.runtimeState.session!.expiresAt += additionalTime;

        // Dispatch session extended event
        this.dispatchEvent(new CustomEvent('sessionExtended', {
            detail: {
                additionalMinutes,
                newExpiresAt: this.runtimeState.session!.expiresAt
            }
        }));

        console.log(`Session extended by ${additionalMinutes} minutes`);
        return true;
    }

    // Get session info
    getSessionInfo(): { isUnlocked: boolean; expiresAt?: number; remainingMinutes?: number } {
        if (!this.isWalletUnlocked()) {
            return { isUnlocked: false };
        }

        const remainingMs = this.runtimeState.session!.expiresAt - Date.now();
        const remainingMinutes = Math.floor(remainingMs / (60 * 1000));

        return {
            isUnlocked: true,
            expiresAt: this.runtimeState.session!.expiresAt,
            remainingMinutes
        };
    }

    // Set new session duration (affects future sessions)
    setSessionDuration(minutes: number): void {
        if (minutes < 1) {
            throw new Error('Session duration must be at least 1 minute');
        }
        this.sessionDuration = minutes * 60 * 1000;
        console.log(`Session duration set to ${minutes} minutes`);
    }

    // Get current session duration setting
    getSessionDuration(): number {
        return this.sessionDuration / (60 * 1000); // return in minutes
    }

    // Generate unique account ID
    private generateAccountId(): string {
        return 'acc_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2, 11);
    }

    // Derive wallet information from private key
    private async deriveWalletFromPrivateKey(
        privateKey: string,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui'
    ): Promise<{ publicKey: string; address?: string; addresses?: any }> {
        try {
            switch (blockchain) {
                case 'bitcoin': {
                    const bitcoin = await import('bitcoinjs-lib');
                    const { ECPairFactory } = await import('ecpair');
                    const ecc = await import('tiny-secp256k1');

                    const ECPair = ECPairFactory(ecc);
                    const keyPair = ECPair.fromPrivateKey(Buffer.from(privateKey.replace('0x', ''), 'hex'));

                    const { address: legacy } = bitcoin.payments.p2pkh({ pubkey: Buffer.from(keyPair.publicKey) });
                    const { address: segwit } = bitcoin.payments.p2sh({
                        redeem: bitcoin.payments.p2wpkh({ pubkey: Buffer.from(keyPair.publicKey) })
                    });
                    const { address: nativeSegwit } = bitcoin.payments.p2wpkh({ pubkey: Buffer.from(keyPair.publicKey) });

                    return {
                        publicKey: Buffer.from(keyPair.publicKey).toString('hex'),
                        addresses: { legacy: legacy || '', segwit: segwit || '', nativeSegwit: nativeSegwit || '' }
                    };
                }

                case 'ethereum': {
                    const { ethers } = await import('ethers');
                    const wallet = new ethers.Wallet(privateKey);
                    return {
                        publicKey: wallet.signingKey.publicKey,
                        address: wallet.address
                    };
                }

                case 'solana': {
                    const { Keypair } = await import('@solana/web3.js');
                    const keyPair = Keypair.fromSecretKey(Buffer.from(privateKey, 'hex'));
                    return {
                        publicKey: keyPair.publicKey.toBase58(),
                        address: keyPair.publicKey.toBase58()
                    };
                }

                case 'sui': {
                    const { Ed25519Keypair } = await import('@mysten/sui/keypairs/ed25519');
                    const keyPair = Ed25519Keypair.fromSecretKey(privateKey);
                    const address = keyPair.getPublicKey().toSuiAddress();
                    return {
                        publicKey: keyPair.getPublicKey().toBase64(),
                        address
                    };
                }

                default:
                    throw new Error(`Unsupported blockchain: ${blockchain}`);
            }
        } catch (error) {
            throw new Error(`Failed to derive wallet from private key: ${error.message}`);
        }
    }

    // Create wallets structure from private key (only for the specified blockchain)
    private async createWalletsFromPrivateKey(
        privateKey: string,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui',
        password: string,
        walletInfo: any
    ): Promise<Account['wallets']> {
        const encryptedPrivateKey = await this.encryptData(privateKey, password);

        // Create empty wallets structure
        const wallets: Account['wallets'] = {
            bitcoin: {
                privateKeyEncrypted: '',
                publicKey: '',
                addresses: { legacy: '', segwit: '', nativeSegwit: '' }
            },
            ethereum: {
                privateKeyEncrypted: '',
                publicKey: '',
                address: ''
            },
            solana: {
                privateKeyEncrypted: '',
                publicKey: ''
            },
            sui: {
                privateKeyEncrypted: '',
                publicKey: ''
            }
        };

        // Fill only the specified blockchain
        switch (blockchain) {
            case 'bitcoin':
                wallets.bitcoin = {
                    privateKeyEncrypted: encryptedPrivateKey,
                    publicKey: walletInfo.publicKey,
                    addresses: walletInfo.addresses
                };
                break;
            case 'ethereum':
                wallets.ethereum = {
                    privateKeyEncrypted: encryptedPrivateKey,
                    publicKey: walletInfo.publicKey,
                    address: walletInfo.address
                };
                break;
            case 'solana':
                wallets.solana = {
                    privateKeyEncrypted: encryptedPrivateKey,
                    publicKey: walletInfo.publicKey
                };
                break;
            case 'sui':
                wallets.sui = {
                    privateKeyEncrypted: encryptedPrivateKey,
                    publicKey: walletInfo.publicKey
                };
                break;
        }

        return wallets;
    }

    // Helper method to get password from session or require explicit password
    private async getPasswordFromSessionOrRequire(explicitPassword?: string): Promise<string> {
        // If explicit password is provided, verify and use it
        if (explicitPassword) {
            const isValid = await this.verifyPassword(explicitPassword);
            if (!isValid) {
                throw new Error('Invalid password');
            }
            return explicitPassword;
        }

        // Check if wallet is unlocked via session
        if (!this.isWalletUnlocked() || !this.runtimeState.session) {
            throw new Error('Wallet is locked. Please unlock wallet or provide password.');
        }

        // Decrypt password from session
        try {
            const sessionSalt = new Uint8Array(
                atob(this.runtimeState.session.salt).split('').map(char => char.charCodeAt(0))
            );
            const password = await this.decryptDataWithSalt(
                this.runtimeState.session.passwordEncrypted,
                sessionSalt
            );
            return password;
        } catch (error) {
            throw new Error('Failed to retrieve password from session. Please unlock wallet again.');
        }
    }

    // Create wallet from seed phrase for all 3 blockchains
    async createWallet(
        seedPhraseIndex: number,
        password: string,
        accountName: string = 'Account 1',
        accountIndex: number = 0
    ): Promise<Account> {
        try {
            // Validate inputs
            if (!password || !accountName) {
                throw new Error('Password and account name are required');
            }

            // Verify password
            const isValidPassword = await this.verifyPassword(password);
            if (!isValidPassword) {
                throw new Error('Invalid password');
            }

            // Validate seed phrase index
            if (seedPhraseIndex < 0 || seedPhraseIndex >= this.state.seedPhraseEncrypted.length) {
                throw new Error('Invalid seed phrase index');
            }

            // Get the seed phrase
            const seedPhrase = await this.getSeedPhrase(seedPhraseIndex, password);

            // Import required libraries
            const bip39 = await import('bip39');

            // Generate seed from mnemonic
            const seed = bip39.mnemonicToSeedSync(seedPhrase);

            // Derive wallets for each blockchain
            const wallets = await this.deriveWalletsFromSeed(seed, accountIndex, password);

            // Encrypt the seed phrase reference for this account
            const fromSeedEncrypted = await this.encryptData(`${seedPhraseIndex}:${accountIndex}`, password);

            // Create wallet account
            const walletAccount: Account = {
                id: this.generateAccountId(),
                name: accountName,
                source: 'seed',
                fromSeedEncrypted,
                derivationIndex: accountIndex,
                seedPhraseIndex,
                wallets
            };

            // Add to accounts array
            this.state.accounts.push(walletAccount);

            // Save to chrome storage
            await chrome.storage.local.set({ walletState: this.state });

            // Dispatch event
            this.dispatchEvent(new CustomEvent('walletCreated', {
                detail: {
                    success: true,
                    accountName,
                    wallets: {
                        bitcoin: {
                            publicKey: wallets.bitcoin.publicKey,
                            addresses: wallets.bitcoin.addresses
                        },
                        ethereum: {
                            publicKey: wallets.ethereum.publicKey,
                            address: wallets.ethereum.address
                        },
                        solana: wallets.solana.publicKey,
                        sui: wallets.sui.publicKey
                    }
                }
            }));

            console.log(`Wallet created successfully for account: ${accountName}`);
            return walletAccount;

        } catch (error) {
            console.error('Failed to create wallet:', error);

            // Dispatch error event
            this.dispatchEvent(new CustomEvent('walletCreationError', {
                detail: { error: error.message }
            }));

            throw error;
        }
    }

    // Helper method to derive wallets from seed
    private async deriveWalletsFromSeed(
        seed: Buffer,
        accountIndex: number,
        password: string
    ): Promise<Account['wallets']> {
        const { ethers } = await import('ethers');
        const { Keypair } = await import('@solana/web3.js');
        const { Ed25519Keypair } = await import('@mysten/sui/keypairs/ed25519');
        const bitcoin = await import('bitcoinjs-lib');
        const { ECPairFactory } = await import('ecpair');
        const ecc = await import('tiny-secp256k1');

        // Bitcoin wallet derivation (BIP44 path: m/44'/0'/0'/0/accountIndex)
        const bitcoinPath = `m/44'/0'/0'/0/${accountIndex}`;
        const bitcoinNode = ethers.HDNodeWallet.fromSeed(seed).derivePath(bitcoinPath);
        const bitcoinPrivateKey = bitcoinNode.privateKey;
        const bitcoinPublicKey = bitcoinNode.publicKey;

        // Create Bitcoin addresses using ECPair and bitcoinjs-lib
        const ECPair = ECPairFactory(ecc);
        const bitcoinKeyPair = ECPair.fromPrivateKey(
            Buffer.from(bitcoinPrivateKey.slice(2), 'hex')
        );

        // Generate all 3 Bitcoin address formats
        const { address: legacyAddress } = bitcoin.payments.p2pkh({
            pubkey: Buffer.from(bitcoinKeyPair.publicKey)
        });

        const { address: segwitAddress } = bitcoin.payments.p2sh({
            redeem: bitcoin.payments.p2wpkh({
                pubkey: Buffer.from(bitcoinKeyPair.publicKey)
            })
        });

        const { address: nativeSegwitAddress } = bitcoin.payments.p2wpkh({
            pubkey: Buffer.from(bitcoinKeyPair.publicKey)
        });

        // Ethereum wallet derivation (BIP44 path: m/44'/60'/0'/0/accountIndex)
        const ethereumPath = `m/44'/60'/0'/0/${accountIndex}`;
        const ethereumWallet = ethers.HDNodeWallet.fromSeed(seed).derivePath(ethereumPath);
        const ethereumPrivateKey = ethereumWallet.privateKey;
        const ethereumPublicKey = ethereumWallet.publicKey;
        const ethereumAddress = ethereumWallet.address; // Proper Ethereum address

        // Solana wallet derivation (BIP44 path: m/44'/501'/accountIndex'/0')
        const solanaPath = `m/44'/501'/${accountIndex}'/0'`;
        const solanaNode = ethers.HDNodeWallet.fromSeed(seed).derivePath(solanaPath);
        const solanaPrivateKeyBytes = ethers.getBytes(solanaNode.privateKey);
        const solanaKeypair = Keypair.fromSeed(solanaPrivateKeyBytes.slice(0, 32));
        const solanaPrivateKey = Buffer.from(solanaKeypair.secretKey).toString('hex');
        const solanaPublicKey = solanaKeypair.publicKey.toBase58();

        // Sui wallet derivation using deriveKeypairFromSeed
        const seedHex = seed.toString('hex');
        const suiPath = `m/44'/784'/0'/0'/${accountIndex}'`;
        const suiKeypair = Ed25519Keypair.deriveKeypairFromSeed(seedHex, suiPath);
        const suiPrivateKey = suiKeypair.getSecretKey();
        const suiPublicKey = suiKeypair.getPublicKey().toSuiAddress();

        // Encrypt private keys
        const bitcoinPrivateKeyEncrypted = await this.encryptData(bitcoinPrivateKey, password);
        const ethereumPrivateKeyEncrypted = await this.encryptData(ethereumPrivateKey, password);
        const solanaPrivateKeyEncrypted = await this.encryptData(solanaPrivateKey, password);
        const suiPrivateKeyEncrypted = await this.encryptData(suiPrivateKey, password);

        return {
            bitcoin: {
                privateKeyEncrypted: bitcoinPrivateKeyEncrypted,
                publicKey: bitcoinPublicKey,
                addresses: {
                    legacy: legacyAddress || '',
                    segwit: segwitAddress || '',
                    nativeSegwit: nativeSegwitAddress || ''
                }
            },
            ethereum: {
                privateKeyEncrypted: ethereumPrivateKeyEncrypted,
                publicKey: ethereumPublicKey,
                address: ethereumAddress
            },
            solana: {
                privateKeyEncrypted: solanaPrivateKeyEncrypted,
                publicKey: solanaPublicKey
            },
            sui: {
                privateKeyEncrypted: suiPrivateKeyEncrypted,
                publicKey: suiPublicKey
            }
        };
    }

    // Get all wallet accounts
    getWalletAccounts(): Account[] {
        return this.state.accounts;
    }

    // Get specific wallet account
    getWalletAccount(index: number): Account | null {
        if (index < 0 || index >= this.state.accounts.length) {
            return null;
        }
        return this.state.accounts[index];
    }

    // Remove wallet account by index
    async removeWalletAccount(index: number, password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        if (index < 0 || index >= this.state.accounts.length) {
            throw new Error('Invalid wallet account index');
        }

        // Remove the wallet account
        const removedAccount = this.state.accounts.splice(index, 1)[0];

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('walletAccountRemoved', {
            detail: {
                removedIndex: index,
                removedAccountId: removedAccount.id,
                removedAccountName: removedAccount.name,
                totalAccounts: this.state.accounts.length
            }
        }));
    }

    // Remove wallet account by ID
    async removeWalletAccountById(accountId: string, password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        const accountIndex = this.state.accounts.findIndex(acc => acc.id === accountId);
        if (accountIndex === -1) {
            throw new Error('Account not found');
        }

        await this.removeWalletAccount(accountIndex, password);
    }

    // Get account by ID
    getAccountById(accountId: string): Account | null {
        return this.state.accounts.find(acc => acc.id === accountId) || null;
    }

    // Get account index by ID
    getAccountIndexById(accountId: string): number {
        return this.state.accounts.findIndex(acc => acc.id === accountId);
    }

    // Get accounts by source type
    getAccountsBySource(source: 'seed' | 'privateKey'): Account[] {
        return this.state.accounts.filter(acc => acc.source === source);
    }

    // Get accounts by seed phrase index
    getAccountsBySeedPhrase(seedPhraseIndex: number): Account[] {
        return this.state.accounts.filter(acc =>
            acc.source === 'seed' && acc.seedPhraseIndex === seedPhraseIndex
        );
    }

    // Update account name
    async updateAccountName(accountId: string, newName: string, password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        const account = this.getAccountById(accountId);
        if (!account) {
            throw new Error('Account not found');
        }

        const oldName = account.name;
        account.name = newName;

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('accountNameUpdated', {
            detail: {
                accountId,
                oldName,
                newName
            }
        }));
    }

    // Move account to specific position
    async moveAccountToPosition(accountId: string, newPosition: number, password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        const currentIndex = this.getAccountIndexById(accountId);
        if (currentIndex === -1) {
            throw new Error('Account not found');
        }

        if (newPosition < 0 || newPosition >= this.state.accounts.length) {
            throw new Error('Invalid position');
        }

        if (currentIndex === newPosition) {
            return; // Already in the correct position
        }

        // Remove account from current position
        const [account] = this.state.accounts.splice(currentIndex, 1);

        // Insert at new position
        this.state.accounts.splice(newPosition, 0, account);

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('accountMoved', {
            detail: {
                accountId,
                accountName: account.name,
                fromPosition: currentIndex,
                toPosition: newPosition
            }
        }));

        console.log(`Moved account "${account.name}" from position ${currentIndex} to ${newPosition}`);
    }

    // Swap positions of two accounts
    async swapAccountPositions(accountId1: string, accountId2: string, password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        const index1 = this.getAccountIndexById(accountId1);
        const index2 = this.getAccountIndexById(accountId2);

        if (index1 === -1 || index2 === -1) {
            throw new Error('One or both accounts not found');
        }

        if (index1 === index2) {
            return; // Same account
        }

        // Swap accounts
        [this.state.accounts[index1], this.state.accounts[index2]] =
            [this.state.accounts[index2], this.state.accounts[index1]];

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('accountsSwapped', {
            detail: {
                account1: { id: accountId1, position: index2 },
                account2: { id: accountId2, position: index1 }
            }
        }));

        console.log(`Swapped positions of accounts ${accountId1} and ${accountId2}`);
    }

    // Move account up by one position
    async moveAccountUp(accountId: string, password: string): Promise<void> {
        const currentIndex = this.getAccountIndexById(accountId);
        if (currentIndex === -1) {
            throw new Error('Account not found');
        }

        if (currentIndex === 0) {
            throw new Error('Account is already at the top');
        }

        await this.moveAccountToPosition(accountId, currentIndex - 1, password);
    }

    // Move account down by one position
    async moveAccountDown(accountId: string, password: string): Promise<void> {
        const currentIndex = this.getAccountIndexById(accountId);
        if (currentIndex === -1) {
            throw new Error('Account not found');
        }

        if (currentIndex === this.state.accounts.length - 1) {
            throw new Error('Account is already at the bottom');
        }

        await this.moveAccountToPosition(accountId, currentIndex + 1, password);
    }

    // Reorder accounts based on array of account IDs
    async reorderAccounts(orderedAccountIds: string[], password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        // Validate that all account IDs exist and no duplicates
        if (orderedAccountIds.length !== this.state.accounts.length) {
            throw new Error('Number of account IDs must match current accounts count');
        }

        const uniqueIds = new Set(orderedAccountIds);
        if (uniqueIds.size !== orderedAccountIds.length) {
            throw new Error('Duplicate account IDs found');
        }

        // Validate all IDs exist
        for (const id of orderedAccountIds) {
            if (this.getAccountIndexById(id) === -1) {
                throw new Error(`Account with ID ${id} not found`);
            }
        }

        // Create new ordered array
        const reorderedAccounts = orderedAccountIds.map(id => {
            const account = this.getAccountById(id);
            if (!account) {
                throw new Error(`Account with ID ${id} not found`);
            }
            return account;
        });

        // Update the accounts array
        this.state.accounts = reorderedAccounts;

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('accountsReordered', {
            detail: {
                newOrder: orderedAccountIds,
                totalAccounts: this.state.accounts.length
            }
        }));

        console.log('Accounts reordered successfully');
    }

    // Sort accounts by name (alphabetically)
    async sortAccountsByName(ascending: boolean = true, password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        const sortedAccounts = [...this.state.accounts].sort((a, b) => {
            const comparison = a.name.localeCompare(b.name);
            return ascending ? comparison : -comparison;
        });

        this.state.accounts = sortedAccounts;

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('accountsSorted', {
            detail: {
                sortBy: 'name',
                ascending,
                totalAccounts: this.state.accounts.length
            }
        }));

        console.log(`Accounts sorted by name (${ascending ? 'ascending' : 'descending'})`);
    }

    // Sort accounts by source type
    async sortAccountsBySource(password: string): Promise<void> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        const sortedAccounts = [...this.state.accounts].sort((a, b) => {
            // Sort order: seed accounts first, then private key accounts
            if (a.source === b.source) {
                return a.name.localeCompare(b.name); // Secondary sort by name
            }
            return a.source === 'seed' ? -1 : 1;
        });

        this.state.accounts = sortedAccounts;

        // Save to chrome storage
        await chrome.storage.local.set({ walletState: this.state });

        // Dispatch event
        this.dispatchEvent(new CustomEvent('accountsSorted', {
            detail: {
                sortBy: 'source',
                totalAccounts: this.state.accounts.length
            }
        }));

        console.log('Accounts sorted by source type');
    }

    // Get account position in the array
    getAccountPosition(accountId: string): number {
        return this.getAccountIndexById(accountId);
    }

    // Get ordered list of account IDs
    getAccountOrder(): string[] {
        return this.state.accounts.map(acc => acc.id);
    }

    // Generate new seed phrase
    async generateSeedPhrase(): Promise<string> {
        const bip39 = await import('bip39');
        return bip39.generateMnemonic();
    }

    // Get decrypted private key for a specific wallet and blockchain (requires password)
    async getPrivateKey(
        accountIndex: number,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui',
        password: string
    ): Promise<string> {
        const isValidPassword = await this.verifyPassword(password);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        if (accountIndex < 0 || accountIndex >= this.state.accounts.length) {
            throw new Error('Invalid account index');
        }

        const account = this.state.accounts[accountIndex];
        const encryptedPrivateKey = account.wallets[blockchain].privateKeyEncrypted;

        return await this.decryptData(encryptedPrivateKey, password);
    }

    // Get decrypted private key using session (no password required if unlocked)
    async getPrivateKeyFromSession(
        accountIndex: number,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui',
        explicitPassword?: string
    ): Promise<string> {
        const password = await this.getPasswordFromSessionOrRequire(explicitPassword);

        if (accountIndex < 0 || accountIndex >= this.state.accounts.length) {
            throw new Error('Invalid account index');
        }

        const account = this.state.accounts[accountIndex];
        const encryptedPrivateKey = account.wallets[blockchain].privateKeyEncrypted;

        return await this.decryptData(encryptedPrivateKey, password);
    }

    // Get decrypted private key by account ID
    async getPrivateKeyById(
        accountId: string,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui',
        explicitPassword?: string
    ): Promise<string> {
        const accountIndex = this.getAccountIndexById(accountId);
        if (accountIndex === -1) {
            throw new Error('Account not found');
        }

        return await this.getPrivateKeyFromSession(accountIndex, blockchain, explicitPassword);
    }

    // Sign data with private key (session-based)
    async signData(
        accountIndex: number,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui',
        data: string | Uint8Array,
        explicitPassword?: string
    ): Promise<string> {
        const privateKey = await this.getPrivateKeyFromSession(accountIndex, blockchain, explicitPassword);

        // This is a simplified signing - in real implementation, use proper signing for each blockchain
        const dataToSign = typeof data === 'string' ? data : Buffer.from(data).toString('hex');
        const signature = `${blockchain}_signature_${privateKey.slice(0, 10)}_${dataToSign.slice(0, 20)}`;

        console.log(`Signed data for ${blockchain} account ${accountIndex}`);
        return signature;
    }

    // Create wallet without requiring password if session is active
    async createWalletFromSession(
        seedPhraseIndex: number,
        accountName: string = 'Account 1',
        accountIndex: number = 0,
        explicitPassword?: string
    ): Promise<Account> {
        const password = await this.getPasswordFromSessionOrRequire(explicitPassword);
        return this.createWallet(seedPhraseIndex, password, accountName, accountIndex);
    }

    // Import account using private key for a specific blockchain
    async importAccountFromPrivateKey(
        privateKey: string,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui',
        accountName: string,
        password: string
    ): Promise<Account> {
        try {
            // Validate password
            const isValidPassword = await this.verifyPassword(password);
            if (!isValidPassword) {
                throw new Error('Invalid password');
            }

            // Validate and derive wallet info from private key
            const walletInfo = await this.deriveWalletFromPrivateKey(privateKey, blockchain);

            // Create account structure
            const walletAccount: Account = {
                id: this.generateAccountId(),
                name: accountName,
                source: 'privateKey',
                wallets: await this.createWalletsFromPrivateKey(privateKey, blockchain, password, walletInfo)
            };

            // Add to accounts array
            this.state.accounts.push(walletAccount);

            // Save to chrome storage
            await chrome.storage.local.set({ walletState: this.state });

            // Dispatch event
            this.dispatchEvent(new CustomEvent('accountImported', {
                detail: {
                    success: true,
                    accountId: walletAccount.id,
                    accountName,
                    blockchain,
                    source: 'privateKey'
                }
            }));

            console.log(`Account imported from ${blockchain} private key: ${accountName}`);
            return walletAccount;

        } catch (error) {
            console.error('Failed to import account from private key:', error);
            this.dispatchEvent(new CustomEvent('accountImportError', {
                detail: { error: error.message }
            }));
            throw error;
        }
    }

    // Import account from existing seed phrase (different from creating new account)
    async importAccountFromSeedPhrase(
        seedPhrase: string,
        accountName: string,
        password: string,
        accountIndex: number = 0
    ): Promise<Account> {
        try {
            // Validate password
            const isValidPassword = await this.verifyPassword(password);
            if (!isValidPassword) {
                throw new Error('Invalid password');
            }

            // Check if seed phrase already exists
            const existingSeedIndex = await this.findExistingSeedPhrase(seedPhrase, password);
            let seedPhraseIndex: number;

            if (existingSeedIndex !== -1) {
                // Use existing seed phrase
                seedPhraseIndex = existingSeedIndex;
            } else {
                // Import new seed phrase
                await this.importSeedPhrase(seedPhrase, password);
                seedPhraseIndex = this.state.seedPhraseEncrypted.length - 1;
            }

            // Create account from seed phrase
            return await this.createWallet(seedPhraseIndex, password, accountName, accountIndex);

        } catch (error) {
            console.error('Failed to import account from seed phrase:', error);
            throw error;
        }
    }

    // Get public key for a specific wallet and blockchain
    getPublicKey(accountIndex: number, blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui'): string {
        if (accountIndex < 0 || accountIndex >= this.state.accounts.length) {
            throw new Error('Invalid account index');
        }

        const account = this.state.accounts[accountIndex];
        return account.wallets[blockchain].publicKey;
    }

    // Get wallet address for different blockchains
    getWalletAddress(
        accountIndex: number,
        blockchain: 'bitcoin' | 'ethereum' | 'solana' | 'sui',
        bitcoinAddressType: 'legacy' | 'segwit' | 'nativeSegwit' = 'nativeSegwit'
    ): string {
        if (accountIndex < 0 || accountIndex >= this.state.accounts.length) {
            throw new Error('Invalid account index');
        }

        const account = this.state.accounts[accountIndex];

        if (blockchain === 'bitcoin') {
            // For Bitcoin, return the specified address type
            return account.wallets.bitcoin.addresses[bitcoinAddressType];
        } else if (blockchain === 'ethereum') {
            // For Ethereum, return the pre-computed address
            return account.wallets.ethereum.address;
        }

        // For Solana and Sui, public key is the address
        return this.getPublicKey(accountIndex, blockchain);
    }

    // Get all Bitcoin addresses for an account
    getBitcoinAddresses(accountIndex: number): {
        legacy: string;
        segwit: string;
        nativeSegwit: string;
    } {
        if (accountIndex < 0 || accountIndex >= this.state.accounts.length) {
            throw new Error('Invalid account index');
        }

        const account = this.state.accounts[accountIndex];
        return account.wallets.bitcoin.addresses;
    }


}