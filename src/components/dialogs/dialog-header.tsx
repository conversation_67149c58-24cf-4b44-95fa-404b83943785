import React from "react";
import { ArrowLeft } from "lucide-react";

const DialogHeader = ({
  icon,
  title,
  onClose,
}: {
  icon?: React.ReactNode;
  title: string;
  onClose: () => void;
}) => {
  return (
    <div className="flex items-center gap-2 justify-between py-2 px-3 border-b border-white/10">
      <button
        onClick={onClose}
        className="size-8 flex items-center justify-center rounded-xl hover:bg-white/10 transition-colors"
      >
        {icon ?? <ArrowLeft className="size-4 text-white" />}
      </button>
      <h1 className="text-xl font-bold">{title}</h1>
      <div className="size-8" />
    </div>
  );
};

export default DialogHeader;
