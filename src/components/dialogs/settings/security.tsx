import React from "react";
import DialogHeader from "../dialog-header";
import { useSettingNavigation } from "../../../store/setting-navigation";
import { motion } from "motion/react";

const Security = () => {
  const { setCurrentPage } = useSettingNavigation();
  return (
    <>
      <DialogHeader title="Security" onClose={() => setCurrentPage("main")} />
      <motion.div
        key="security"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.15 }}
      >
        <button onClick={() => setCurrentPage("privacy")}>Privacy</button>
      </motion.div>
    </>
  );
};

export default Security;
