import React from "react";
import Security from "./security";
import MainSettings from "./main-settings";
import { motion, AnimatePresence } from "motion/react";
import { useSettingNavigation } from "../../../store/setting-navigation";
import LockTime from "./lock-time";

const SettingsDialog = () => {
  const { currentPage } = useSettingNavigation();

  return (
    <AnimatePresence mode="wait">
      {currentPage === "main" && <MainSettings />}
      {currentPage === "security" && <Security />}
      {currentPage === "privacy" && <LockTime />}
    </AnimatePresence>
  );
};

export default SettingsDialog;
