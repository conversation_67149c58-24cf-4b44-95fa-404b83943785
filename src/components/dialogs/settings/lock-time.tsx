import React from "react";
import DialogHeader from "../dialog-header";
import { useSettingNavigation } from "../../../store/setting-navigation";
import { motion } from "motion/react";

const LockTime = () => {
  const { setCurrentPage } = useSettingNavigation();
  return (
    <>
      <DialogHeader
        title="Lock Time"
        onClose={() => setCurrentPage("security")}
      />
      <motion.div
        key="privacy"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.15 }}
      >
        <button>Change Password</button>
      </motion.div>
    </>
  );
};

export default LockTime;
