import React from "react";
import { useSettingNavigation } from "../../../store/setting-navigation";
import DialogHeader from "../dialog-header";
import useDialogStore from "../../../store/dialog-store";
import { AppWindow, ChevronRightIcon, XIcon } from "lucide-react";
import { motion } from "motion/react";

const MainSettings = () => {
  const { closeDialog } = useDialogStore();
  const { setCurrentPage } = useSettingNavigation();
  return (
    <>
      <DialogHeader
        title="Settings"
        onClose={() => closeDialog()}
        icon={<XIcon className="size-4 text-white" />}
      />
      <motion.div
        key="main"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.15 }}
        className="p-2"
      >
        <div className="bg-gray-800 rounded-2xl">
          <button className="flex items-center gap-3 pl-4 w-full">
            <AppWindow />
            <div className={`flex-grow flex items-center gap-3 py-4 pr-4`}>
              <p className="text-sm w-full text-left">Connected DApps</p>
              <ChevronRightIcon className="size-5" />
            </div>
          </button>
        </div>
      </motion.div>
    </>
  );
};

export default MainSettings;
