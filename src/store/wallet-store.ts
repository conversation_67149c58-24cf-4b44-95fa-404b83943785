import { create } from 'zustand';

export interface WalletAccount {
    address: string;
    name: string;
    isActive: boolean;
}

export interface WalletState {
    accounts: WalletAccount[];
    activeAccount: string | null;
    isLocked: boolean;
    isConnected: boolean;
    isLoading: boolean;
    error: string | null;
}

interface WalletActions {
    // State management
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;

    // Wallet operations
    createWallet: (password: string, accountName?: string) => Promise<void>;
    importWallet: (privateKey: string, password: string, accountName: string) => Promise<void>;
    unlockWallet: (password: string) => Promise<void>;
    lockWallet: () => Promise<void>;

    // Account operations
    getWalletState: () => Promise<void>;
    switchAccount: (address: string) => Promise<void>;

    // Transaction operations
    signTransaction: (transactionData: any, password: string) => Promise<string>;
}

type WalletStore = WalletState & WalletActions;

const useWalletStore = create<WalletStore>((set, get) => ({
    // Initial state
    accounts: [],
    activeAccount: null,
    isLocked: true,
    isConnected: false,
    isLoading: false,
    error: null,

    // Actions
    setLoading: (loading: boolean) => set({ isLoading: loading }),

    setError: (error: string | null) => set({ error }),

    createWallet: async (password: string, accountName?: string) => {
        set({ isLoading: true, error: null });

        try {
            const response = await sendMessageToBackground({
                type: 'CREATE_WALLET',
                data: { password, accountName }
            });

            if (response.success) {
                await get().getWalletState();
            } else {
                throw new Error(response.error || 'Failed to create wallet');
            }
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to create wallet' });
            throw error;
        } finally {
            set({ isLoading: false });
        }
    },

    importWallet: async (privateKey: string, password: string, accountName: string) => {
        set({ isLoading: true, error: null });

        try {
            const response = await sendMessageToBackground({
                type: 'IMPORT_WALLET',
                data: { privateKey, password, accountName }
            });

            if (response.success) {
                await get().getWalletState();
            } else {
                throw new Error(response.error || 'Failed to import wallet');
            }
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to import wallet' });
            throw error;
        } finally {
            set({ isLoading: false });
        }
    },

    unlockWallet: async (password: string) => {
        set({ isLoading: true, error: null });

        try {
            const response = await sendMessageToBackground({
                type: 'UNLOCK_WALLET',
                data: { password }
            });

            if (response.success) {
                set({
                    accounts: response.data.accounts || [],
                    activeAccount: response.data.activeAccount,
                    isLocked: false,
                    isConnected: true
                });
            } else {
                throw new Error(response.error || 'Failed to unlock wallet');
            }
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to unlock wallet' });
            throw error;
        } finally {
            set({ isLoading: false });
        }
    },

    lockWallet: async () => {
        set({ isLoading: true, error: null });

        try {
            const response = await sendMessageToBackground({
                type: 'LOCK_WALLET'
            });

            if (response.success) {
                set({
                    accounts: [],
                    activeAccount: null,
                    isLocked: true,
                    isConnected: false
                });
            } else {
                throw new Error(response.error || 'Failed to lock wallet');
            }
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to lock wallet' });
            throw error;
        } finally {
            set({ isLoading: false });
        }
    },

    getWalletState: async () => {
        set({ isLoading: true, error: null });

        try {
            const response = await sendMessageToBackground({
                type: 'GET_WALLET_STATE'
            });

            if (response.success) {
                set({
                    accounts: response.data.accounts || [],
                    activeAccount: response.data.activeAccount,
                    isLocked: response.data.isLocked,
                    isConnected: !response.data.isLocked && response.data.accounts.length > 0
                });
            } else {
                throw new Error(response.error || 'Failed to get wallet state');
            }
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to get wallet state' });
        } finally {
            set({ isLoading: false });
        }
    },

    switchAccount: async (address: string) => {
        set({ isLoading: true, error: null });

        try {
            const response = await sendMessageToBackground({
                type: 'SWITCH_ACCOUNT',
                data: { address }
            });

            if (response.success) {
                set({ activeAccount: address });
            } else {
                throw new Error(response.error || 'Failed to switch account');
            }
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to switch account' });
            throw error;
        } finally {
            set({ isLoading: false });
        }
    },

    signTransaction: async (transactionData: any, password: string): Promise<string> => {
        set({ isLoading: true, error: null });

        try {
            const response = await sendMessageToBackground({
                type: 'SIGN_TRANSACTION',
                data: { transactionData, password }
            });

            if (response.success) {
                return response.data.signature;
            } else {
                throw new Error(response.error || 'Failed to sign transaction');
            }
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to sign transaction' });
            throw error;
        } finally {
            set({ isLoading: false });
        }
    }
}));

// Helper function to send messages to background script
function sendMessageToBackground(message: any): Promise<any> {
    return new Promise((resolve) => {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                resolve({
                    success: false,
                    error: chrome.runtime.lastError.message || 'Communication error'
                });
            } else {
                resolve(response);
            }
        });
    });
}

export default useWalletStore; 