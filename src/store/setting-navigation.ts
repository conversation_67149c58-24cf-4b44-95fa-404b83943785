import { create } from "zustand";

interface SettingNavigation {
    currentPage: "main" | "security" | "privacy" | "notifications" | "language" | "about";
}

interface SettingNavigationActions {
    setCurrentPage: (page: "main" | "security" | "privacy" | "notifications" | "language" | "about") => void;
}

export const useSettingNavigation = create<SettingNavigation & SettingNavigationActions>((set) => ({
    currentPage: "main",
    setCurrentPage: (page) => set({ currentPage: page }),
}));