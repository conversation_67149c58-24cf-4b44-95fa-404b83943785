<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wallet Creation Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .wallet-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .blockchain {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🏦 Multi-Blockchain Wallet Creation Demo</h1>
    
    <div class="container">
        <h2>Step 1: Generate Seed Phrase</h2>
        <button onclick="generateSeedPhrase()">Generate New Seed Phrase</button>
        <div id="seedPhraseOutput" class="output"></div>
        
        <h3>Or use custom seed phrase:</h3>
        <textarea id="customSeedPhrase" placeholder="Enter your 12-24 word seed phrase here..." rows="3"></textarea>
    </div>

    <div class="container">
        <h2>Step 2: Set Password</h2>
        <input type="password" id="password" placeholder="Enter password (min 8 characters)" />
        <button onclick="importSeedPhrase()">Import Seed Phrase</button>
        <div id="importOutput" class="output"></div>
    </div>

    <div class="container">
        <h2>Step 3: Create Wallet Account</h2>
        <input type="text" id="accountName" placeholder="Account name" value="My First Account" />
        <input type="number" id="accountIndex" placeholder="Account index" value="0" min="0" />
        <button onclick="createWallet()" id="createWalletBtn" disabled>Create Wallet</button>
        <div id="walletOutput" class="output"></div>
    </div>

    <div class="container">
        <h2>Step 4: View Wallet Information</h2>
        <button onclick="showWalletInfo()" id="showInfoBtn" disabled>Show Wallet Info</button>
        <button onclick="showPrivateKeys()" id="showKeysBtn" disabled>Show Private Keys (Careful!)</button>
        <div id="infoOutput" class="output"></div>
    </div>

    <div class="container">
        <h2>Demo Actions</h2>
        <button onclick="runFullDemo()">Run Full Demo</button>
        <button onclick="clearAll()">Clear All</button>
        <div id="demoOutput" class="output"></div>
    </div>

    <script type="module">
        // Mock chrome storage for demo
        window.chrome = {
            storage: {
                local: {
                    data: {},
                    get: async function(keys) {
                        console.log('Mock storage get:', keys);
                        return this.data;
                    },
                    set: async function(data) {
                        console.log('Mock storage set:', Object.keys(data));
                        Object.assign(this.data, data);
                    }
                }
            }
        };

        // Import WalletManager (this would need to be built properly in real app)
        // For demo purposes, we'll show the expected behavior
        
        let walletManager = null;
        let currentSeedPhrase = '';
        let currentPassword = '';
        let isWalletCreated = false;

        window.generateSeedPhrase = async function() {
            try {
                // Simulate seed phrase generation
                const words = [
                    'abandon', 'ability', 'able', 'about', 'above', 'absent', 'absorb', 'abstract',
                    'absurd', 'abuse', 'access', 'accident', 'account', 'accuse', 'achieve', 'acid',
                    'acoustic', 'acquire', 'across', 'act', 'action', 'actor', 'actress', 'actual'
                ];
                
                const seedPhrase = Array.from({length: 12}, () => 
                    words[Math.floor(Math.random() * words.length)]
                ).join(' ');
                
                currentSeedPhrase = seedPhrase;
                document.getElementById('seedPhraseOutput').innerHTML = 
                    `<span class="success">Generated seed phrase:</span>\n${seedPhrase}`;
                document.getElementById('customSeedPhrase').value = seedPhrase;
            } catch (error) {
                document.getElementById('seedPhraseOutput').innerHTML = 
                    `<span class="error">Error: ${error.message}</span>`;
            }
        };

        window.importSeedPhrase = async function() {
            try {
                const seedPhrase = document.getElementById('customSeedPhrase').value.trim();
                const password = document.getElementById('password').value;

                if (!seedPhrase) {
                    throw new Error('Please enter a seed phrase');
                }
                if (password.length < 8) {
                    throw new Error('Password must be at least 8 characters');
                }

                currentSeedPhrase = seedPhrase;
                currentPassword = password;

                document.getElementById('importOutput').innerHTML = 
                    `<span class="success">Seed phrase imported successfully!</span>\nWords: ${seedPhrase.split(' ').length}`;
                
                document.getElementById('createWalletBtn').disabled = false;
            } catch (error) {
                document.getElementById('importOutput').innerHTML = 
                    `<span class="error">Error: ${error.message}</span>`;
            }
        };

        window.createWallet = async function() {
            try {
                const accountName = document.getElementById('accountName').value || 'My Account';
                const accountIndex = parseInt(document.getElementById('accountIndex').value) || 0;

                // Simulate wallet creation
                const mockWallet = {
                    name: accountName,
                    wallets: {
                        ethereum: {
                            publicKey: '0x' + Array.from({length: 40}, () => Math.floor(Math.random() * 16).toString(16)).join(''),
                            privateKeyEncrypted: 'encrypted_eth_key_' + Date.now()
                        },
                        solana: {
                            publicKey: Array.from({length: 44}, () => 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'[Math.floor(Math.random() * 62)]).join(''),
                            privateKeyEncrypted: 'encrypted_sol_key_' + Date.now()
                        },
                        sui: {
                            publicKey: '0x' + Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join(''),
                            privateKeyEncrypted: 'encrypted_sui_key_' + Date.now()
                        }
                    }
                };

                isWalletCreated = true;
                window.currentWallet = mockWallet;

                document.getElementById('walletOutput').innerHTML = 
                    `<span class="success">Wallet created successfully!</span>\n\n` +
                    `Account: ${mockWallet.name}\n\n` +
                    `<div class="blockchain">🔷 Ethereum\nPublic Key: ${mockWallet.wallets.ethereum.publicKey}</div>` +
                    `<div class="blockchain">🟣 Solana\nAddress: ${mockWallet.wallets.solana.publicKey}</div>` +
                    `<div class="blockchain">🔵 Sui\nAddress: ${mockWallet.wallets.sui.publicKey}</div>`;

                document.getElementById('showInfoBtn').disabled = false;
                document.getElementById('showKeysBtn').disabled = false;
            } catch (error) {
                document.getElementById('walletOutput').innerHTML = 
                    `<span class="error">Error: ${error.message}</span>`;
            }
        };

        window.showWalletInfo = function() {
            if (!isWalletCreated) {
                document.getElementById('infoOutput').innerHTML = 
                    `<span class="error">No wallet created yet</span>`;
                return;
            }

            document.getElementById('infoOutput').innerHTML = 
                `<span class="success">Wallet Information:</span>\n\n` +
                `Total Seed Phrases: 1\n` +
                `Total Accounts: 1\n` +
                `Has Wallet: true\n\n` +
                `Current Account: ${window.currentWallet.name}\n` +
                `Supported Blockchains: Ethereum, Solana, Sui`;
        };

        window.showPrivateKeys = function() {
            if (!isWalletCreated) {
                document.getElementById('infoOutput').innerHTML = 
                    `<span class="error">No wallet created yet</span>`;
                return;
            }

            const wallet = window.currentWallet;
            document.getElementById('infoOutput').innerHTML = 
                `<span class="error">⚠️ PRIVATE KEYS (Handle with extreme care!):</span>\n\n` +
                `Ethereum Private Key: ${wallet.wallets.ethereum.privateKeyEncrypted.slice(0, 20)}...\n` +
                `Solana Private Key: ${wallet.wallets.solana.privateKeyEncrypted.slice(0, 20)}...\n` +
                `Sui Private Key: ${wallet.wallets.sui.privateKeyEncrypted.slice(0, 20)}...\n\n` +
                `<span class="error">Note: These are encrypted. In real implementation, you would decrypt them with the password.</span>`;
        };

        window.runFullDemo = async function() {
            document.getElementById('demoOutput').innerHTML = 'Running full demo...\n';
            
            try {
                // Step 1: Generate seed phrase
                await generateSeedPhrase();
                document.getElementById('demoOutput').innerHTML += '✅ Step 1: Seed phrase generated\n';
                
                // Step 2: Set password and import
                document.getElementById('password').value = 'demoPassword123';
                await importSeedPhrase();
                document.getElementById('demoOutput').innerHTML += '✅ Step 2: Seed phrase imported\n';
                
                // Step 3: Create wallet
                await createWallet();
                document.getElementById('demoOutput').innerHTML += '✅ Step 3: Wallet created\n';
                
                document.getElementById('demoOutput').innerHTML += '\n🎉 Full demo completed successfully!';
            } catch (error) {
                document.getElementById('demoOutput').innerHTML += `\n❌ Demo failed: ${error.message}`;
            }
        };

        window.clearAll = function() {
            document.getElementById('seedPhraseOutput').innerHTML = '';
            document.getElementById('importOutput').innerHTML = '';
            document.getElementById('walletOutput').innerHTML = '';
            document.getElementById('infoOutput').innerHTML = '';
            document.getElementById('demoOutput').innerHTML = '';
            document.getElementById('customSeedPhrase').value = '';
            document.getElementById('password').value = '';
            document.getElementById('accountName').value = 'My First Account';
            document.getElementById('accountIndex').value = '0';
            document.getElementById('createWalletBtn').disabled = true;
            document.getElementById('showInfoBtn').disabled = true;
            document.getElementById('showKeysBtn').disabled = true;
            
            currentSeedPhrase = '';
            currentPassword = '';
            isWalletCreated = false;
            window.currentWallet = null;
        };

        // Initialize
        console.log('Wallet Demo initialized');
    </script>
</body>
</html>
