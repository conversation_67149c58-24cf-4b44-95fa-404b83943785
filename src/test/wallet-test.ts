// Simple test file to verify wallet creation functionality
// This is for testing purposes only - not for production use

import { WalletManager } from '../types/wallet';

// Mock chrome.storage for testing
(global as any).chrome = {
    storage: {
        local: {
            get: async (keys: string[]) => {
                // Return empty state for testing
                return {};
            },
            set: async (data: any) => {
                console.log('Mock storage set:', Object.keys(data));
                return;
            }
        }
    }
};

async function testWalletCreation() {
    console.log('🧪 Starting Wallet Creation Test...\n');

    try {
        // Initialize wallet manager
        const walletManager = new WalletManager();
        
        // Wait a bit for initialization
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log('✅ WalletManager initialized');

        // Test 1: Generate seed phrase
        console.log('\n📝 Test 1: Generate seed phrase');
        const seedPhrase = await walletManager.generateSeedPhrase();
        console.log('Generated seed phrase:', seedPhrase);
        console.log('Word count:', seedPhrase.split(' ').length);

        // Test 2: Import seed phrase
        console.log('\n🔐 Test 2: Import seed phrase');
        const password = 'testPassword123';
        const seedPhraseId = await walletManager.importSeedPhrase(seedPhrase, password);
        console.log('Seed phrase imported with ID:', seedPhraseId);

        // Test 3: Create wallet account
        console.log('\n🏦 Test 3: Create wallet account');
        const walletAccount = await walletManager.createWallet(
            0, // First seed phrase
            password,
            'Test Account',
            0 // First account
        );

        console.log('Wallet account created successfully!');
        console.log('Account name:', walletAccount.name);
        console.log('Ethereum public key length:', walletAccount.wallets.ethereum.publicKey.length);
        console.log('Solana address length:', walletAccount.wallets.solana.publicKey.length);
        console.log('Sui address length:', walletAccount.wallets.sui.publicKey.length);

        // Test 4: Verify encrypted private keys exist
        console.log('\n🔒 Test 4: Verify encrypted private keys');
        console.log('Ethereum private key encrypted:', !!walletAccount.wallets.ethereum.privateKeyEncrypted);
        console.log('Solana private key encrypted:', !!walletAccount.wallets.solana.privateKeyEncrypted);
        console.log('Sui private key encrypted:', !!walletAccount.wallets.sui.privateKeyEncrypted);

        // Test 5: Get private keys
        console.log('\n🔓 Test 5: Decrypt private keys');
        const ethPrivateKey = await walletManager.getPrivateKey(0, 'ethereum', password);
        const solPrivateKey = await walletManager.getPrivateKey(0, 'solana', password);
        const suiPrivateKey = await walletManager.getPrivateKey(0, 'sui', password);

        console.log('Ethereum private key length:', ethPrivateKey.length);
        console.log('Solana private key length:', solPrivateKey.length);
        console.log('Sui private key length:', suiPrivateKey.length);

        // Test 6: Create second account from same seed
        console.log('\n👥 Test 6: Create second account');
        const secondAccount = await walletManager.createWallet(
            0, // Same seed phrase
            password,
            'Second Account',
            1 // Second account index
        );

        console.log('Second account created!');
        console.log('Different from first account:', 
            secondAccount.wallets.ethereum.publicKey !== walletAccount.wallets.ethereum.publicKey);

        // Test 7: Wallet state
        console.log('\n📊 Test 7: Check wallet state');
        console.log('Total seed phrases:', walletManager.getSeedPhraseCount());
        console.log('Total accounts:', walletManager.getWalletAccounts().length);
        console.log('Has wallet:', walletManager.isHasWallet());

        // Test 8: Error handling
        console.log('\n❌ Test 8: Error handling');
        try {
            await walletManager.getPrivateKey(0, 'ethereum', 'wrongPassword');
            console.log('ERROR: Should have thrown error for wrong password');
        } catch (error) {
            console.log('✅ Correctly threw error for wrong password:', error.message);
        }

        console.log('\n🎉 All tests passed successfully!');

        return {
            seedPhrase,
            walletAccount,
            secondAccount,
            privateKeys: {
                ethereum: ethPrivateKey.slice(0, 10) + '...',
                solana: solPrivateKey.slice(0, 10) + '...',
                sui: suiPrivateKey.slice(0, 10) + '...'
            }
        };

    } catch (error) {
        console.error('❌ Test failed:', error);
        throw error;
    }
}

// Test with known seed phrase for consistency
async function testWithKnownSeed() {
    console.log('\n🔬 Testing with known seed phrase...\n');

    try {
        const walletManager = new WalletManager();
        await new Promise(resolve => setTimeout(resolve, 100));

        // Use a known test seed phrase (DO NOT use in production!)
        const knownSeed = 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about';
        const password = 'testPassword123';

        await walletManager.importSeedPhrase(knownSeed, password);
        const walletAccount = await walletManager.createWallet(0, password, 'Known Seed Account', 0);

        console.log('Known seed test results:');
        console.log('Ethereum public key:', walletAccount.wallets.ethereum.publicKey);
        console.log('Solana address:', walletAccount.wallets.solana.publicKey);
        console.log('Sui address:', walletAccount.wallets.sui.publicKey);

        return walletAccount;

    } catch (error) {
        console.error('Known seed test failed:', error);
        throw error;
    }
}

// Export test functions
export { testWalletCreation, testWithKnownSeed };

// Run tests if this file is executed directly
if (typeof window === 'undefined' && typeof process !== 'undefined') {
    // Node.js environment
    testWalletCreation()
        .then(() => testWithKnownSeed())
        .then(() => {
            console.log('\n✅ All tests completed successfully!');
        })
        .catch((error) => {
            console.error('\n❌ Tests failed:', error);
            process.exit(1);
        });
}
