import React from "react";
import { createRoot } from "react-dom/client";
import "../../index.css";
import Dialog from "../../components/dialog";
import useDialogStore from "../../store/dialog-store";
import SettingsDialog from "../../components/dialogs/settings/settings-dialog";

const MainScreen = () => {
  const { openDialog } = useDialogStore();

  return (
    <div>
      <button onClick={() => openDialog(<SettingsDialog />)}>
        Open Dialog
      </button>
      <Dialog />
    </div>
  );
};

const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  root.render(<MainScreen />);
}
