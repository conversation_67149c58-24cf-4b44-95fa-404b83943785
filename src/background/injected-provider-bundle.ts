// Injected Provider Bundle - All-in-one file for injection
// This file contains all provider implementations to avoid import issues

// EIP-6963 Provider Implementation
interface EIP6963ProviderInfo {
    uuid: string;
    name: string;
    icon: string;
    rdns: string;
}

interface EIP6963ProviderDetail {
    info: EIP6963ProviderInfo;
    provider: any;
}

interface EIP6963AnnounceProviderEvent extends CustomEvent {
    type: "eip6963:announceProvider";
    detail: EIP6963ProviderDetail;
}

interface EIP6963RequestProviderEvent extends CustomEvent {
    type: "eip6963:requestProvider";
}

class EIP6963Provider {
    private providerInfo: EIP6963ProviderInfo;
    private provider: any;

    constructor(provider: any) {
        this.provider = provider;
        this.providerInfo = {
            uuid: "350670db-19fa-4704-a166-e52e178b59d2",
            name: "<PERSON><PERSON><PERSON>",
            icon: this.getIconDataUri(),
            rdns: "io.purro.wallet"
        };

        this.init();
    }

    private init() {
        window.addEventListener("eip6963:requestProvider", this.handleProviderRequest.bind(this));

        this.announceProvider();

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.announceProvider();
            });
        }

        setTimeout(() => {
            this.announceProvider();
        }, 100);

        setTimeout(() => {
            this.announceProvider();
        }, 1000);
    }

    private handleProviderRequest(event: EIP6963RequestProviderEvent) {
        this.announceProvider();
    }

    private announceProvider() {
        const announceEvent = new CustomEvent(
            "eip6963:announceProvider",
            {
                detail: {
                    info: this.providerInfo,
                    provider: this.provider
                }
            }
        ) as EIP6963AnnounceProviderEvent;

        window.dispatchEvent(announceEvent);
    }

    private getIconDataUri(): string {
        const svgIcon = `
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <rect width="32" height="32" rx="8" fill="#6366f1"/>
        <path d="M8 12h16v8H8z" fill="white"/>
        <circle cx="20" cy="16" r="2" fill="#6366f1"/>
      </svg>
    `;

        return `data:image/svg+xml;base64,${btoa(svgIcon)}`;
    }
}

// Ethereum Provider Implementation (EIP-1193)
interface RequestArguments {
    method: string;
    params?: unknown[] | object;
}

interface ProviderRpcError extends Error {
    code: number;
    data?: unknown;
}

interface EthereumProvider {
    request(args: RequestArguments): Promise<unknown>;
    on(eventName: string, listener: (...args: any[]) => void): this;
    removeListener(eventName: string, listener: (...args: any[]) => void): this;
    enable?(): Promise<string[]>;
    send?(method: string, params?: any[]): Promise<any>;
    sendAsync?(payload: any, callback: (error: any, result: any) => void): void;
    isMetaMask?: boolean;
    isPurro?: boolean;
    chainId?: string;
    networkVersion?: string;
    selectedAddress?: string | null;
}

class PurroEthereumProvider implements EthereumProvider {
    public isPurro = true;
    public isMetaMask = false;
    public chainId = '0x1';
    public networkVersion = '1';
    public selectedAddress: string | null = null;

    private eventListeners: Map<string, Function[]> = new Map();
    private purroProvider: any;

    constructor(purroProvider: any) {
        this.purroProvider = purroProvider;
        this.init();
    }

    private init() {
        this.purroProvider.on('connect', (data: any) => {
            this.selectedAddress = data.activeAccount;
            this.emit('connect', { chainId: this.chainId });
            this.emit('accountsChanged', data.accounts || []);
        });

        this.purroProvider.on('disconnect', () => {
            this.selectedAddress = null;
            this.emit('disconnect');
            this.emit('accountsChanged', []);
        });

        this.purroProvider.on('accountsChanged', (accounts: string[]) => {
            this.selectedAddress = accounts[0] || null;
            this.emit('accountsChanged', accounts);
        });

        this.purroProvider.on('accountChanged', (account: string) => {
            this.selectedAddress = account;
            this.emit('accountsChanged', [account]);
        });
    }

    async request(args: RequestArguments): Promise<unknown> {
        const { method, params } = args;

        try {
            switch (method) {
                case 'eth_requestAccounts':
                case 'eth_accounts':
                    return await this.handleAccountsRequest();

                case 'eth_chainId':
                    return this.chainId;

                case 'net_version':
                    return this.networkVersion;

                case 'eth_sendTransaction':
                    return await this.handleSendTransaction(params);

                case 'eth_signTransaction':
                    return await this.handleSignTransaction(params);

                case 'personal_sign':
                    return await this.handlePersonalSign(params);

                case 'eth_sign':
                    return await this.handleEthSign(params);

                case 'eth_signTypedData':
                case 'eth_signTypedData_v3':
                case 'eth_signTypedData_v4':
                    return await this.handleSignTypedData(params);

                case 'wallet_switchEthereumChain':
                    return await this.handleSwitchChain(params);

                case 'wallet_addEthereumChain':
                    return await this.handleAddChain(params);

                default:
                    throw this.createProviderError(4200, `Method ${method} not supported`);
            }
        } catch (error) {
            if (error instanceof Error && 'code' in error) {
                throw error;
            }
            throw this.createProviderError(4001, error instanceof Error ? error.message : 'Unknown error');
        }
    }

    private async handleAccountsRequest(): Promise<string[]> {
        if (!this.purroProvider.isConnected) {
            const result = await this.purroProvider.connect();
            return result.accounts || [];
        }
        return await this.purroProvider.getAccounts();
    }

    private async handleSendTransaction(params: any): Promise<string> {
        if (!params || !Array.isArray(params) || params.length === 0) {
            throw this.createProviderError(4001, 'Invalid transaction parameters');
        }

        const txData = params[0];
        return await this.purroProvider.signTransaction(txData);
    }

    private async handleSignTransaction(params: any): Promise<string> {
        if (!params || !Array.isArray(params) || params.length === 0) {
            throw this.createProviderError(4001, 'Invalid transaction parameters');
        }

        const txData = params[0];
        return await this.purroProvider.signTransaction(txData);
    }

    private async handlePersonalSign(params: any): Promise<string> {
        if (!params || !Array.isArray(params) || params.length < 2) {
            throw this.createProviderError(4001, 'Invalid sign parameters');
        }

        const [message, address] = params;
        return await this.purroProvider.signTransaction({ message, address, type: 'personal_sign' });
    }

    private async handleEthSign(params: any): Promise<string> {
        if (!params || !Array.isArray(params) || params.length < 2) {
            throw this.createProviderError(4001, 'Invalid sign parameters');
        }

        const [address, message] = params;
        return await this.purroProvider.signTransaction({ message, address, type: 'eth_sign' });
    }

    private async handleSignTypedData(params: any): Promise<string> {
        if (!params || !Array.isArray(params) || params.length < 2) {
            throw this.createProviderError(4001, 'Invalid typed data parameters');
        }

        const [address, typedData] = params;
        return await this.purroProvider.signTransaction({ typedData, address, type: 'typed_data' });
    }

    private async handleSwitchChain(params: any): Promise<null> {
        if (!params || !Array.isArray(params) || params.length === 0) {
            throw this.createProviderError(4001, 'Invalid chain parameters');
        }

        const { chainId } = params[0];
        this.chainId = chainId;
        this.networkVersion = parseInt(chainId, 16).toString();
        this.emit('chainChanged', chainId);
        return null;
    }

    private async handleAddChain(params: any): Promise<null> {
        return null;
    }

    private createProviderError(code: number, message: string, data?: unknown): ProviderRpcError {
        const error = new Error(message) as ProviderRpcError;
        error.code = code;
        if (data !== undefined) {
            error.data = data;
        }
        return error;
    }

    on(eventName: string, listener: (...args: any[]) => void): this {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, []);
        }
        this.eventListeners.get(eventName)!.push(listener);
        return this;
    }

    removeListener(eventName: string, listener: (...args: any[]) => void): this {
        const listeners = this.eventListeners.get(eventName);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
        return this;
    }

    private emit(eventName: string, ...args: any[]): void {
        const listeners = this.eventListeners.get(eventName);
        if (listeners) {
            listeners.forEach(listener => {
                try {
                    listener(...args);
                } catch (error) {
                    console.error('Error in event listener:', error);
                }
            });
        }
    }

    async enable(): Promise<string[]> {
        return await this.handleAccountsRequest();
    }

    async send(method: string, params?: any[]): Promise<any> {
        return await this.request({ method, params });
    }

    sendAsync(payload: any, callback: (error: any, result: any) => void): void {
        this.request({ method: payload.method, params: payload.params })
            .then(result => callback(null, { id: payload.id, jsonrpc: '2.0', result }))
            .catch(error => callback(error, null));
    }
}

// Purro Provider Implementation
interface PurroProvider {
    isConnected: boolean;
    accounts: string[];
    activeAccount: string | null;

    connect(): Promise<{ accounts: string[]; activeAccount: string | null }>;
    disconnect(): Promise<void>;
    getAccounts(): Promise<string[]>;
    switchAccount(address: string): Promise<void>;
    signTransaction(transactionData: any): Promise<string>;
    on(event: string, callback: Function): void;
    off(event: string, callback: Function): void;
    isUnlocked(): Promise<boolean>;
}

class PurroWalletProvider implements PurroProvider {
    public isConnected: boolean = false;
    public accounts: string[] = [];
    public activeAccount: string | null = null;

    private eventListeners: Map<string, Function[]> = new Map();
    private requestId: number = 0;
    private pendingRequests: Map<string, { resolve: Function; reject: Function }> = new Map();

    constructor() {
        this.init();
    }

    private init() {
        window.addEventListener('message', (event) => {
            if (event.source !== window) return;
            if (event.data.source !== 'purro-content-script') return;
            if (event.data.type !== 'RESPONSE') return;

            const { requestId, response } = event.data;
            const pendingRequest = this.pendingRequests.get(requestId);

            if (pendingRequest) {
                this.pendingRequests.delete(requestId);

                if (response.success) {
                    pendingRequest.resolve(response.data);
                } else {
                    pendingRequest.reject(new Error(response.error || 'Unknown error'));
                }
            }
        });

        this.checkConnectionState();
    }

    private async checkConnectionState() {
        try {
            const state = await this.sendMessage('GET_WALLET_STATE');
            if (state && !state.isLocked && state.accounts.length > 0) {
                this.isConnected = true;
                this.accounts = state.accounts.map((acc: any) => acc.address);
                this.activeAccount = state.activeAccount;
                this.emit('accountsChanged', this.accounts);
            }
        } catch (error) {
            console.log('Wallet not connected or locked');
        }
    }

    private generateRequestId(): string {
        return `purro_${++this.requestId}_${Date.now()}`;
    }

    private sendMessage(type: string, data?: any): Promise<any> {
        return new Promise((resolve, reject) => {
            const requestId = this.generateRequestId();

            this.pendingRequests.set(requestId, { resolve, reject });

            window.postMessage({
                source: 'purro-provider',
                type,
                data,
                requestId
            }, '*');

            setTimeout(() => {
                if (this.pendingRequests.has(requestId)) {
                    this.pendingRequests.delete(requestId);
                    reject(new Error('Request timeout'));
                }
            }, 30000);
        });
    }

    async connect(): Promise<{ accounts: string[]; activeAccount: string | null }> {
        try {
            const result = await this.sendMessage('CONNECT_WALLET');

            this.isConnected = true;
            this.accounts = result.accounts || [];
            this.activeAccount = result.activeAccount || null;

            this.emit('connect', { accounts: this.accounts, activeAccount: this.activeAccount });
            this.emit('accountsChanged', this.accounts);

            return {
                accounts: this.accounts,
                activeAccount: this.activeAccount
            };
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    async disconnect(): Promise<void> {
        try {
            await this.sendMessage('DISCONNECT_WALLET');

            this.isConnected = false;
            this.accounts = [];
            this.activeAccount = null;

            this.emit('disconnect');
            this.emit('accountsChanged', []);
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    async getAccounts(): Promise<string[]> {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }

        try {
            const result = await this.sendMessage('GET_ACCOUNTS');
            this.accounts = result.accounts.map((acc: any) => acc.address);
            return this.accounts;
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    async switchAccount(address: string): Promise<void> {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }

        try {
            await this.sendMessage('SWITCH_ACCOUNT', { address });
            this.activeAccount = address;
            this.emit('accountChanged', address);
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    async signTransaction(transactionData: any): Promise<string> {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }

        try {
            const password = await this.promptForPassword();
            const result = await this.sendMessage('SIGN_TRANSACTION', {
                transactionData,
                password
            });

            return result.signature;
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }

    async isUnlocked(): Promise<boolean> {
        try {
            const state = await this.sendMessage('GET_WALLET_STATE');
            return !state.isLocked;
        } catch (error) {
            return false;
        }
    }

    private async promptForPassword(): Promise<string> {
        return new Promise((resolve, reject) => {
            const password = prompt('Enter your wallet password to sign this transaction:');
            if (password) {
                resolve(password);
            } else {
                reject(new Error('Password required to sign transaction'));
            }
        });
    }

    on(event: string, callback: Function): void {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event)!.push(callback);
    }

    off(event: string, callback: Function): void {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    private emit(event: string, data?: any): void {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Error in event listener:', error);
                }
            });
        }
    }
}

// Initialize and expose providers
const purroProvider = new PurroWalletProvider();
const ethereumProvider = new PurroEthereumProvider(purroProvider);

// Expose providers to window object
(window as any).purro = purroProvider;
(window as any).ethereum = ethereumProvider;

// Initialize EIP-6963 provider with Ethereum provider
const eip6963Provider = new EIP6963Provider(ethereumProvider);

// Dispatch ready events
window.dispatchEvent(new CustomEvent('purro#initialized', {
    detail: purroProvider
}));

window.dispatchEvent(new CustomEvent('ethereum#initialized', {
    detail: ethereumProvider
}));

console.log('Purro wallet provider injected with EIP-6963 and Ethereum support'); 