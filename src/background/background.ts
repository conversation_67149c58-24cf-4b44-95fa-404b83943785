// Background script for Purro wallet extension
import { <PERSON><PERSON>and<PERSON> } from "./message-handler";
import { WalletManager } from "./wallet-manager";

class BackgroundService {
    private walletManager: WalletManager;
    private messageHandler: MessageHandler;

    constructor() {
        this.walletManager = new WalletManager();
        this.messageHandler = new MessageHandler(this.walletManager);
        this.init();
    }

    private init() {
        // Listen for extension installation
        chrome.runtime.onInstalled.addListener(() => {
            console.log('Purro wallet extension installed');
        });

        // Listen for messages from content scripts and popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.messageHandler.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Listen for tab updates to inject content script
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.injectContentScript(tabId, tab.url);
            }
        });
    }

    private async injectContentScript(tabId: number, url: string) {
        try {
            // Only inject on web pages (not chrome:// or extension pages)
            if (url.startsWith('http://') || url.startsWith('https://')) {
                // Check if content script is already injected
                const results = await chrome.scripting.executeScript({
                    target: { tabId },
                    func: () => {
                        return window.hasOwnProperty('purroContentScriptInjected');
                    }
                });

                // Only inject if not already injected
                if (!results[0]?.result) {
                    await chrome.scripting.executeScript({
                        target: { tabId },
                        files: ['contentScript.js']
                    });
                }
            }
        } catch (error) {
            // Ignore errors for tabs that can't be accessed (like chrome:// pages)
            if (!error.message.includes('Cannot access')) {
                console.error('Failed to inject content script:', error);
            }
        }
    }
}

// Initialize background service
new BackgroundService(); 