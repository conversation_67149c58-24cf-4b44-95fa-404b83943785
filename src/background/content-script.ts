// Content Script - Injected into web pages to provide wallet functionality
import { Message, MessageResponse } from './message-handler';

// Mark that content script has been injected to avoid double injection
(window as any).purroContentScriptInjected = true;

// Inject the provider script into the page
const script = document.createElement('script');
script.src = chrome.runtime.getURL('injectedProviderBundle.js');
script.onload = function () {
    // Remove the script element after injection
    if (script.parentNode) {
        script.parentNode.removeChild(script);
    }
};
(document.head || document.documentElement).appendChild(script);

// Listen for messages from the injected provider
window.addEventListener('message', async (event) => {
    // Only accept messages from the same origin
    if (event.source !== window) return;

    // Only handle messages from our provider
    if (event.data.source !== 'purro-provider') return;

    const { type, data, requestId } = event.data;

    try {
        // Forward the message to background script
        const response = await sendMessageToBackground({
            type,
            data,
            requestId
        });

        // Send response back to the injected provider
        window.postMessage({
            source: 'purro-content-script',
            type: 'RESPONSE',
            requestId,
            response
        }, '*');
    } catch (error) {
        // Send error response back to the injected provider
        window.postMessage({
            source: 'purro-content-script',
            type: 'RESPONSE',
            requestId,
            response: {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        }, '*');
    }
});

// Helper function to send messages to background script
function sendMessageToBackground(message: Message): Promise<MessageResponse> {
    return new Promise((resolve) => {
        chrome.runtime.sendMessage(message, (response: MessageResponse) => {
            if (chrome.runtime.lastError) {
                resolve({
                    success: false,
                    error: chrome.runtime.lastError.message || 'Communication error'
                });
            } else {
                resolve(response);
            }
        });
    });
}

// Listen for disconnect events
chrome.runtime.onConnect.addListener((port) => {
    port.onDisconnect.addListener(() => {
        // Handle disconnection if needed
        console.log('Content script disconnected');
    });
});

console.log('Purro wallet content script loaded'); 