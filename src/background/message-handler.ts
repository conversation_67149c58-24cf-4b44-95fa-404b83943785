// Message Handler for communication between background, content script, and popup
import { WalletManager } from './wallet-manager';

export interface Message {
    type: string;
    data?: any;
    requestId?: string;
}

export interface MessageResponse {
    success: boolean;
    data?: any;
    error?: string;
    requestId?: string;
}

export class MessageHandler {
    private walletManager: WalletManager;

    constructor(walletManager: WalletManager) {
        this.walletManager = walletManager;
    }

    async handleMessage(
        message: Message,
        sender: chrome.runtime.MessageSender,
        sendResponse: (response: MessageResponse) => void
    ): Promise<void> {
        try {
            let response: MessageResponse;

            switch (message.type) {
                case 'GET_WALLET_STATE':
                    response = await this.handleGetWalletState();
                    break;

                case 'CREATE_WALLET':
                    response = await this.handleCreateWallet(message.data);
                    break;

                case 'IMPORT_WALLET':
                    response = await this.handleImportWallet(message.data);
                    break;

                case 'UNLOCK_WALLET':
                    response = await this.handleUnlockWallet(message.data);
                    break;

                case 'LOCK_WALLET':
                    response = await this.handleLockWallet();
                    break;

                case 'SIGN_TRANSACTION':
                    response = await this.handleSignTransaction(message.data);
                    break;

                case 'CONNECT_WALLET':
                    response = await this.handleConnectWallet(sender);
                    break;

                case 'DISCONNECT_WALLET':
                    response = await this.handleDisconnectWallet(sender);
                    break;

                case 'GET_ACCOUNTS':
                    response = await this.handleGetAccounts();
                    break;

                case 'SWITCH_ACCOUNT':
                    response = await this.handleSwitchAccount(message.data);
                    break;

                default:
                    response = {
                        success: false,
                        error: `Unknown message type: ${message.type}`,
                        requestId: message.requestId
                    };
            }

            response.requestId = message.requestId;
            sendResponse(response);
        } catch (error) {
            sendResponse({
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                requestId: message.requestId
            });
        }
    }

    private async handleGetWalletState(): Promise<MessageResponse> {
        const state = this.walletManager.getWalletState();
        return {
            success: true,
            data: state
        };
    }

    private async handleCreateWallet(data: { password: string; accountName?: string }): Promise<MessageResponse> {
        try {
            const account = await this.walletManager.createWallet(data.password, data.accountName);
            return {
                success: true,
                data: { ...account, privateKey: '' } // Don't expose private key
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to create wallet'
            };
        }
    }

    private async handleImportWallet(data: { privateKey: string; password: string; accountName: string }): Promise<MessageResponse> {
        try {
            const account = await this.walletManager.importWallet(data.privateKey, data.password, data.accountName);
            return {
                success: true,
                data: { ...account, privateKey: '' } // Don't expose private key
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to import wallet'
            };
        }
    }

    private async handleUnlockWallet(data: { password: string }): Promise<MessageResponse> {
        try {
            const success = await this.walletManager.unlockWallet(data.password);
            if (success) {
                const state = this.walletManager.getWalletState();
                return {
                    success: true,
                    data: state
                };
            } else {
                return {
                    success: false,
                    error: 'Invalid password'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to unlock wallet'
            };
        }
    }

    private async handleLockWallet(): Promise<MessageResponse> {
        try {
            await this.walletManager.lockWallet();
            return {
                success: true,
                data: { message: 'Wallet locked successfully' }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to lock wallet'
            };
        }
    }

    private async handleSignTransaction(data: { transactionData: any; password: string }): Promise<MessageResponse> {
        try {
            const signature = await this.walletManager.signTransaction(data.transactionData, data.password);
            return {
                success: true,
                data: { signature }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to sign transaction'
            };
        }
    }

    private async handleConnectWallet(sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            const state = this.walletManager.getWalletState();

            if (state.isLocked) {
                return {
                    success: false,
                    error: 'Wallet is locked'
                };
            }

            // Store connected site information
            if (sender.tab?.url) {
                const url = new URL(sender.tab.url);
                const origin = url.origin;

                // In real implementation, you might want to ask user for permission
                await this.storeConnectedSite(origin);
            }

            return {
                success: true,
                data: {
                    accounts: state.accounts.map(acc => acc.address),
                    activeAccount: state.activeAccount
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to connect wallet'
            };
        }
    }

    private async handleDisconnectWallet(sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            if (sender.tab?.url) {
                const url = new URL(sender.tab.url);
                const origin = url.origin;
                await this.removeConnectedSite(origin);
            }

            return {
                success: true,
                data: { message: 'Wallet disconnected successfully' }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to disconnect wallet'
            };
        }
    }

    private async handleGetAccounts(): Promise<MessageResponse> {
        try {
            const state = this.walletManager.getWalletState();
            return {
                success: true,
                data: {
                    accounts: state.accounts.map(acc => ({
                        address: acc.address,
                        name: acc.name,
                        isActive: acc.isActive
                    })),
                    activeAccount: state.activeAccount
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to get accounts'
            };
        }
    }

    private async handleSwitchAccount(data: { address: string }): Promise<MessageResponse> {
        try {
            // In real implementation, add method to WalletManager to switch accounts
            return {
                success: true,
                data: { message: 'Account switched successfully' }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to switch account'
            };
        }
    }

    private async storeConnectedSite(origin: string): Promise<void> {
        try {
            const result = await chrome.storage.local.get(['connectedSites']);
            const connectedSites = result.connectedSites || [];

            if (!connectedSites.includes(origin)) {
                connectedSites.push(origin);
                await chrome.storage.local.set({ connectedSites });
            }
        } catch (error) {
            console.error('Failed to store connected site:', error);
        }
    }

    private async removeConnectedSite(origin: string): Promise<void> {
        try {
            const result = await chrome.storage.local.get(['connectedSites']);
            const connectedSites = result.connectedSites || [];

            const updatedSites = connectedSites.filter((site: string) => site !== origin);
            await chrome.storage.local.set({ connectedSites: updatedSites });
        } catch (error) {
            console.error('Failed to remove connected site:', error);
        }
    }
} 