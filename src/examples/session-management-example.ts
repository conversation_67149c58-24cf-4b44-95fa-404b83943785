// Example usage of WalletManager with Session Management

import { WalletManager } from '../types/wallet';

// Example function to demonstrate session-based wallet operations
export async function exampleSessionManagement() {
    try {
        console.log('🔐 Session Management Example\n');

        // Initialize wallet manager with custom session duration (15 minutes)
        const walletManager = new WalletManager(15);

        // Step 1: Setup wallet (one-time)
        console.log('📝 Step 1: Setting up wallet...');
        const seedPhrase = await walletManager.generateSeedPhrase();
        const password = 'mySecurePassword123';
        
        await walletManager.importSeedPhrase(seedPhrase, password);
        console.log('✅ Seed phrase imported');

        // Step 2: Unlock wallet (creates session)
        console.log('\n🔓 Step 2: Unlocking wallet...');
        const unlocked = await walletManager.unlockWallet(password);
        if (!unlocked) {
            throw new Error('Failed to unlock wallet');
        }
        console.log('✅ Wallet unlocked with 15-minute session');

        // Step 3: Create wallet without password (using session)
        console.log('\n🏦 Step 3: Creating wallet using session...');
        const walletAccount = await walletManager.createWalletFromSession(
            0, // First seed phrase
            'My Session Account',
            0  // First account
        );
        console.log('✅ Wallet created using session (no password required)');
        console.log('Ethereum address:', walletAccount.wallets.ethereum.publicKey);

        // Step 4: Get private keys using session
        console.log('\n🔑 Step 4: Getting private keys using session...');
        const ethPrivateKey = await walletManager.getPrivateKeyFromSession(0, 'ethereum');
        const solPrivateKey = await walletManager.getPrivateKeyFromSession(0, 'solana');
        const suiPrivateKey = await walletManager.getPrivateKeyFromSession(0, 'sui');
        
        console.log('✅ Private keys retrieved using session:');
        console.log('Ethereum:', ethPrivateKey.slice(0, 10) + '...');
        console.log('Solana:', solPrivateKey.slice(0, 10) + '...');
        console.log('Sui:', suiPrivateKey.slice(0, 10) + '...');

        // Step 5: Sign data using session
        console.log('\n✍️ Step 5: Signing data using session...');
        const dataToSign = 'Hello, blockchain world!';
        const ethSignature = await walletManager.signData(0, 'ethereum', dataToSign);
        const solSignature = await walletManager.signData(0, 'solana', dataToSign);
        const suiSignature = await walletManager.signData(0, 'sui', dataToSign);

        console.log('✅ Data signed using session:');
        console.log('Ethereum signature:', ethSignature);
        console.log('Solana signature:', solSignature);
        console.log('Sui signature:', suiSignature);

        // Step 6: Check session info
        console.log('\n📊 Step 6: Session information...');
        const sessionInfo = walletManager.getSessionInfo();
        console.log('Session unlocked:', sessionInfo.isUnlocked);
        console.log('Remaining minutes:', sessionInfo.remainingMinutes);
        console.log('Expires at:', new Date(sessionInfo.expiresAt!).toLocaleTimeString());

        // Step 7: Extend session
        console.log('\n⏰ Step 7: Extending session...');
        const extended = walletManager.extendSession(10); // Add 10 more minutes
        if (extended) {
            const newSessionInfo = walletManager.getSessionInfo();
            console.log('✅ Session extended by 10 minutes');
            console.log('New remaining minutes:', newSessionInfo.remainingMinutes);
        }

        // Step 8: Demonstrate session expiry handling
        console.log('\n⏳ Step 8: Testing session expiry...');
        
        // Temporarily set a very short session for demo
        walletManager.setSessionDuration(0.1); // 0.1 minutes = 6 seconds
        await walletManager.unlockWallet(password);
        console.log('✅ Wallet unlocked with 6-second session for demo');

        // Wait for session to expire
        console.log('Waiting 7 seconds for session to expire...');
        await new Promise(resolve => setTimeout(resolve, 7000));

        // Try to use expired session
        try {
            await walletManager.getPrivateKeyFromSession(0, 'ethereum');
            console.log('❌ ERROR: Should have failed due to expired session');
        } catch (error) {
            console.log('✅ Correctly handled expired session:', error.message);
        }

        // Step 9: Manual lock/unlock
        console.log('\n🔒 Step 9: Manual lock/unlock...');
        await walletManager.unlockWallet(password);
        console.log('Wallet unlocked');
        
        walletManager.lockWallet();
        console.log('Wallet manually locked');

        try {
            await walletManager.getPrivateKeyFromSession(0, 'ethereum');
            console.log('❌ ERROR: Should have failed due to locked wallet');
        } catch (error) {
            console.log('✅ Correctly handled locked wallet:', error.message);
        }

        // Step 10: Fallback to explicit password
        console.log('\n🔑 Step 10: Using explicit password when locked...');
        const ethPrivateKeyWithPassword = await walletManager.getPrivateKeyFromSession(
            0, 
            'ethereum', 
            password // Explicit password
        );
        console.log('✅ Retrieved private key with explicit password:', ethPrivateKeyWithPassword.slice(0, 10) + '...');

        console.log('\n🎉 Session management example completed successfully!');

        return {
            walletAccount,
            sessionInfo: walletManager.getSessionInfo(),
            sessionDuration: walletManager.getSessionDuration()
        };

    } catch (error) {
        console.error('❌ Session management example failed:', error);
        throw error;
    }
}

// Example function to demonstrate different session durations
export async function exampleDifferentSessionDurations() {
    console.log('\n⏰ Testing Different Session Durations\n');

    try {
        // Test different session durations
        const durations = [1, 5, 30, 60]; // minutes

        for (const duration of durations) {
            console.log(`Testing ${duration}-minute session...`);
            
            const walletManager = new WalletManager(duration);
            const seedPhrase = await walletManager.generateSeedPhrase();
            const password = 'testPassword123';
            
            await walletManager.importSeedPhrase(seedPhrase, password);
            await walletManager.unlockWallet(password);
            
            const sessionInfo = walletManager.getSessionInfo();
            console.log(`✅ ${duration}-minute session created, expires in ${sessionInfo.remainingMinutes} minutes`);
        }

        console.log('\n✅ All session duration tests passed!');

    } catch (error) {
        console.error('❌ Session duration test failed:', error);
        throw error;
    }
}

// Example function to demonstrate event handling
export async function exampleSessionEvents() {
    console.log('\n📡 Session Events Example\n');

    try {
        const walletManager = new WalletManager(1); // 1 minute session

        // Set up event listeners
        walletManager.addEventListener('walletUnlocked', (event) => {
            console.log('🔓 Event: Wallet unlocked', event.detail);
        });

        walletManager.addEventListener('walletLocked', (event) => {
            console.log('🔒 Event: Wallet locked', event.detail);
        });

        walletManager.addEventListener('sessionExtended', (event) => {
            console.log('⏰ Event: Session extended', event.detail);
        });

        // Trigger events
        const seedPhrase = await walletManager.generateSeedPhrase();
        const password = 'eventTestPassword';
        
        await walletManager.importSeedPhrase(seedPhrase, password);
        await walletManager.unlockWallet(password);
        
        walletManager.extendSession(2);
        walletManager.lockWallet();

        console.log('✅ Event handling example completed!');

    } catch (error) {
        console.error('❌ Event handling example failed:', error);
        throw error;
    }
}

// Export all examples
export const sessionExamples = {
    exampleSessionManagement,
    exampleDifferentSessionDurations,
    exampleSessionEvents
};
