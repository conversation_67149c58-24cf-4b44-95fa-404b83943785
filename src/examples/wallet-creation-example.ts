// Example usage of WalletManager for creating wallets for Ethereum, Solana, and Sui

import { WalletManager } from '../types/wallet';

// Example function to demonstrate wallet creation
export async function exampleWalletCreation() {
    try {
        // Initialize wallet manager
        const walletManager = new WalletManager();

        // Step 1: Generate a new seed phrase
        console.log('Generating new seed phrase...');
        const seedPhrase = await walletManager.generateSeedPhrase();
        console.log('Generated seed phrase:', seedPhrase);

        // Step 2: Import the seed phrase with a password
        const password = 'mySecurePassword123';
        console.log('Importing seed phrase...');
        const seedPhraseId = await walletManager.importSeedPhrase(seedPhrase, password);
        console.log('Seed phrase imported with ID:', seedPhraseId);

        // Step 3: Create wallet accounts for all 3 blockchains
        console.log('Creating wallet account...');
        const walletAccount = await walletManager.createWallet(
            0, // Use first seed phrase (index 0)
            password,
            'My First Account',
            0 // Account index 0
        );

        console.log('Wallet account created successfully!');
        console.log('Account name:', walletAccount.name);
        
        // Display public keys/addresses for each blockchain
        console.log('\n=== Wallet Addresses ===');
        console.log('Ethereum Public Key:', walletAccount.wallets.ethereum.publicKey);
        console.log('Solana Address:', walletAccount.wallets.solana.publicKey);
        console.log('Sui Address:', walletAccount.wallets.sui.publicKey);

        // Step 4: Create additional accounts from the same seed phrase
        console.log('\nCreating second account...');
        const secondAccount = await walletManager.createWallet(
            0, // Same seed phrase
            password,
            'My Second Account',
            1 // Account index 1
        );

        console.log('Second account created!');
        console.log('Account name:', secondAccount.name);
        console.log('Ethereum Public Key:', secondAccount.wallets.ethereum.publicKey);
        console.log('Solana Address:', secondAccount.wallets.solana.publicKey);
        console.log('Sui Address:', secondAccount.wallets.sui.publicKey);

        // Step 5: Demonstrate getting private keys (be careful with this in production!)
        console.log('\n=== Private Keys (Handle with care!) ===');
        
        const ethPrivateKey = await walletManager.getPrivateKey(0, 'ethereum', password);
        console.log('Ethereum Private Key:', ethPrivateKey.slice(0, 10) + '...');
        
        const solPrivateKey = await walletManager.getPrivateKey(0, 'solana', password);
        console.log('Solana Private Key:', solPrivateKey.slice(0, 10) + '...');
        
        const suiPrivateKey = await walletManager.getPrivateKey(0, 'sui', password);
        console.log('Sui Private Key:', suiPrivateKey.slice(0, 10) + '...');

        // Step 6: Show wallet state
        console.log('\n=== Wallet State ===');
        console.log('Total seed phrases:', walletManager.getSeedPhraseCount());
        console.log('Total accounts:', walletManager.getWalletAccounts().length);
        console.log('Has wallet:', walletManager.isHasWallet());

        return {
            seedPhrase,
            walletAccount,
            secondAccount
        };

    } catch (error) {
        console.error('Error in wallet creation example:', error);
        throw error;
    }
}

// Example function to demonstrate importing an existing seed phrase
export async function exampleImportExistingSeed() {
    try {
        const walletManager = new WalletManager();

        // Example seed phrase (DO NOT use this in production!)
        const existingSeedPhrase = 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about';
        const password = 'myPassword123';

        console.log('Importing existing seed phrase...');
        const seedPhraseId = await walletManager.importSeedPhrase(existingSeedPhrase, password);
        
        console.log('Creating wallet from imported seed...');
        const walletAccount = await walletManager.createWallet(0, password, 'Imported Account', 0);

        console.log('Wallet created from imported seed:');
        console.log('Ethereum Public Key:', walletAccount.wallets.ethereum.publicKey);
        console.log('Solana Address:', walletAccount.wallets.solana.publicKey);
        console.log('Sui Address:', walletAccount.wallets.sui.publicKey);

        return walletAccount;

    } catch (error) {
        console.error('Error in import example:', error);
        throw error;
    }
}

// Example function to demonstrate multiple seed phrases
export async function exampleMultipleSeedPhrases() {
    try {
        const walletManager = new WalletManager();
        const password = 'myPassword123';

        // Import first seed phrase
        const seedPhrase1 = await walletManager.generateSeedPhrase();
        await walletManager.importSeedPhrase(seedPhrase1, password);
        const account1 = await walletManager.createWallet(0, password, 'Account from Seed 1', 0);

        // Import second seed phrase
        const seedPhrase2 = await walletManager.generateSeedPhrase();
        await walletManager.importSeedPhrase(seedPhrase2, password);
        const account2 = await walletManager.createWallet(1, password, 'Account from Seed 2', 0);

        console.log('Created accounts from multiple seed phrases:');
        console.log('Account 1 (Ethereum):', account1.wallets.ethereum.publicKey);
        console.log('Account 2 (Ethereum):', account2.wallets.ethereum.publicKey);

        return { account1, account2 };

    } catch (error) {
        console.error('Error in multiple seeds example:', error);
        throw error;
    }
}
