// Advanced Account Management Examples

import { WalletManager } from '../types/wallet';

// Example function to demonstrate advanced account management
export async function exampleAdvancedAccountManagement() {
    try {
        console.log('🏦 Advanced Account Management Example\n');

        // Initialize wallet manager
        const walletManager = new WalletManager(30);
        const password = 'advancedPassword123';

        // Step 1: Create multiple seed phrases
        console.log('📝 Step 1: Creating multiple seed phrases...');
        const seedPhrase1 = await walletManager.generateSeedPhrase();
        const seedPhrase2 = await walletManager.generateSeedPhrase();

        await walletManager.importSeedPhrase(seedPhrase1, password);
        await walletManager.importSeedPhrase(seedPhrase2, password);
        
        console.log('✅ Two seed phrases imported');

        // Step 2: Unlock wallet
        await walletManager.unlockWallet(password);
        console.log('✅ Wallet unlocked');

        // Step 3: Create accounts from different seed phrases
        console.log('\n👥 Step 3: Creating accounts from different seed phrases...');
        
        const account1 = await walletManager.createWalletFromSession(0, 'Seed 1 - Account 1', 0);
        const account2 = await walletManager.createWalletFromSession(0, 'Seed 1 - Account 2', 1);
        const account3 = await walletManager.createWalletFromSession(1, 'Seed 2 - Account 1', 0);

        console.log('✅ Created 3 accounts from 2 seed phrases');
        console.log('Account IDs:', [account1.id, account2.id, account3.id]);

        // Step 4: Import account from private key (Bitcoin)
        console.log('\n🔑 Step 4: Importing account from Bitcoin private key...');
        const btcPrivateKey = '5KJvsngHeMpm884wtkJNzQGaCErckhHJBGFsvd3VyK5qMZXj3hS'; // Example private key
        
        try {
            const importedBtcAccount = await walletManager.importAccountFromPrivateKey(
                btcPrivateKey,
                'bitcoin',
                'Imported Bitcoin Account',
                password
            );
            console.log('✅ Bitcoin account imported:', importedBtcAccount.id);
            console.log('Bitcoin addresses:', importedBtcAccount.wallets.bitcoin.addresses);
        } catch (error) {
            console.log('⚠️ Bitcoin import failed (expected with example key):', error.message);
        }

        // Step 5: Import account from private key (Ethereum)
        console.log('\n🔷 Step 5: Importing account from Ethereum private key...');
        const ethPrivateKey = '0x4c0883a69102937d6231471b5dbb6204fe5129617082792ae468d01a3f362318';
        
        try {
            const importedEthAccount = await walletManager.importAccountFromPrivateKey(
                ethPrivateKey,
                'ethereum',
                'Imported Ethereum Account',
                password
            );
            console.log('✅ Ethereum account imported:', importedEthAccount.id);
            console.log('Ethereum address:', importedEthAccount.wallets.ethereum.address);
        } catch (error) {
            console.log('⚠️ Ethereum import failed (expected with example key):', error.message);
        }

        // Step 6: Import account from existing seed phrase
        console.log('\n🌱 Step 6: Importing account from existing seed phrase...');
        const existingSeedAccount = await walletManager.importAccountFromSeedPhrase(
            seedPhrase1, // Use existing seed phrase
            'Imported from Existing Seed',
            password,
            2 // Different derivation index
        );
        console.log('✅ Account imported from existing seed:', existingSeedAccount.id);

        // Step 7: Demonstrate account management by ID
        console.log('\n🆔 Step 7: Account management by ID...');
        
        // Get account by ID
        const foundAccount = walletManager.getAccountById(account1.id);
        console.log('Found account by ID:', foundAccount?.name);

        // Get accounts by source
        const seedAccounts = walletManager.getAccountsBySource('seed');
        const privateKeyAccounts = walletManager.getAccountsBySource('privateKey');
        console.log('Seed-based accounts:', seedAccounts.length);
        console.log('Private key accounts:', privateKeyAccounts.length);

        // Get accounts by seed phrase
        const seed1Accounts = walletManager.getAccountsBySeedPhrase(0);
        const seed2Accounts = walletManager.getAccountsBySeedPhrase(1);
        console.log('Accounts from seed 1:', seed1Accounts.length);
        console.log('Accounts from seed 2:', seed2Accounts.length);

        // Step 8: Update account name
        console.log('\n✏️ Step 8: Updating account name...');
        await walletManager.updateAccountName(account1.id, 'Renamed Account', password);
        const updatedAccount = walletManager.getAccountById(account1.id);
        console.log('Updated account name:', updatedAccount?.name);

        // Step 9: Get private keys by account ID
        console.log('\n🔐 Step 9: Getting private keys by account ID...');
        try {
            const btcPrivKey = await walletManager.getPrivateKeyById(account1.id, 'bitcoin');
            const ethPrivKey = await walletManager.getPrivateKeyById(account1.id, 'ethereum');
            console.log('✅ Retrieved private keys by account ID');
            console.log('Bitcoin private key:', btcPrivKey.slice(0, 10) + '...');
            console.log('Ethereum private key:', ethPrivKey.slice(0, 10) + '...');
        } catch (error) {
            console.log('⚠️ Private key retrieval failed:', error.message);
        }

        // Step 10: Display account summary
        console.log('\n📊 Step 10: Account Summary...');
        const allAccounts = walletManager.getWalletAccounts();
        
        console.log(`Total accounts: ${allAccounts.length}`);
        allAccounts.forEach((acc, index) => {
            console.log(`${index + 1}. ${acc.name} (${acc.id})`);
            console.log(`   Source: ${acc.source}`);
            if (acc.source === 'seed') {
                console.log(`   Seed phrase: ${acc.seedPhraseIndex}, Derivation: ${acc.derivationIndex}`);
            }
            console.log(`   Bitcoin: ${acc.wallets.bitcoin.addresses.nativeSegwit || 'N/A'}`);
            console.log(`   Ethereum: ${acc.wallets.ethereum.address || 'N/A'}`);
            console.log('');
        });

        // Step 11: Remove account by ID
        console.log('\n🗑️ Step 11: Removing account by ID...');
        if (allAccounts.length > 3) {
            const accountToRemove = allAccounts[allAccounts.length - 1];
            await walletManager.removeWalletAccountById(accountToRemove.id, password);
            console.log(`✅ Removed account: ${accountToRemove.name}`);
        }

        console.log('\n🎉 Advanced account management example completed!');

        return {
            totalAccounts: walletManager.getWalletAccounts().length,
            seedPhrases: walletManager.getSeedPhraseCount(),
            accountsBySource: {
                seed: walletManager.getAccountsBySource('seed').length,
                privateKey: walletManager.getAccountsBySource('privateKey').length
            }
        };

    } catch (error) {
        console.error('❌ Advanced account management example failed:', error);
        throw error;
    }
}

// Example function to demonstrate account organization
export async function exampleAccountOrganization() {
    console.log('\n📁 Account Organization Example\n');

    try {
        const walletManager = new WalletManager(15);
        const password = 'organizationPassword123';

        // Create organized account structure
        const seedPhrase = await walletManager.generateSeedPhrase();
        await walletManager.importSeedPhrase(seedPhrase, password);
        await walletManager.unlockWallet(password);

        // Create accounts with organized naming
        const accounts = [];
        const accountTypes = [
            { name: 'Main Wallet', index: 0 },
            { name: 'Trading Account', index: 1 },
            { name: 'DeFi Portfolio', index: 2 },
            { name: 'NFT Collection', index: 3 },
            { name: 'Cold Storage', index: 4 }
        ];

        for (const type of accountTypes) {
            const account = await walletManager.createWalletFromSession(
                0,
                type.name,
                type.index
            );
            accounts.push(account);
        }

        console.log('Created organized account structure:');
        accounts.forEach(acc => {
            console.log(`- ${acc.name} (${acc.id})`);
            console.log(`  Bitcoin: ${acc.wallets.bitcoin.addresses.nativeSegwit}`);
            console.log(`  Ethereum: ${acc.wallets.ethereum.address}`);
        });

        return accounts;

    } catch (error) {
        console.error('❌ Account organization example failed:', error);
        throw error;
    }
}

// Example function to demonstrate multi-blockchain private key imports
export async function exampleMultiBlockchainImports() {
    console.log('\n🔗 Multi-Blockchain Import Example\n');

    try {
        const walletManager = new WalletManager(20);
        const password = 'multiBlockchainPassword123';

        // Initialize wallet
        const seedPhrase = await walletManager.generateSeedPhrase();
        await walletManager.importSeedPhrase(seedPhrase, password);
        await walletManager.unlockWallet(password);

        // Example private keys for different blockchains (DO NOT use in production!)
        const privateKeys = {
            bitcoin: '5KJvsngHeMpm884wtkJNzQGaCErckhHJBGFsvd3VyK5qMZXj3hS',
            ethereum: '0x4c0883a69102937d6231471b5dbb6204fe5129617082792ae468d01a3f362318',
            // Note: Solana and Sui private keys would be in different formats
        };

        const importedAccounts = [];

        // Try importing each blockchain type
        for (const [blockchain, privateKey] of Object.entries(privateKeys)) {
            try {
                const account = await walletManager.importAccountFromPrivateKey(
                    privateKey,
                    blockchain as any,
                    `Imported ${blockchain.charAt(0).toUpperCase() + blockchain.slice(1)} Account`,
                    password
                );
                importedAccounts.push(account);
                console.log(`✅ ${blockchain} account imported: ${account.id}`);
            } catch (error) {
                console.log(`⚠️ ${blockchain} import failed: ${error.message}`);
            }
        }

        console.log(`\nSuccessfully imported ${importedAccounts.length} accounts from private keys`);

        return importedAccounts;

    } catch (error) {
        console.error('❌ Multi-blockchain import example failed:', error);
        throw error;
    }
}

// Export all examples
export const advancedAccountExamples = {
    exampleAdvancedAccountManagement,
    exampleAccountOrganization,
    exampleMultiBlockchainImports
};
