// Example usage of WalletManager for Bitcoin wallet creation

import { WalletManager } from '../types/wallet';

// Example function to demonstrate Bitcoin wallet creation
export async function exampleBitcoinWalletCreation() {
    try {
        console.log('₿ Bitcoin Wallet Creation Example\n');

        // Initialize wallet manager with 30-minute session
        const walletManager = new WalletManager(30);

        // Step 1: Generate a new seed phrase
        console.log('📝 Step 1: Generating new seed phrase...');
        const seedPhrase = await walletManager.generateSeedPhrase();
        console.log('Generated seed phrase:', seedPhrase);

        // Step 2: Import the seed phrase with a password
        const password = 'myBitcoinPassword123';
        console.log('\n🔐 Step 2: Importing seed phrase...');
        const seedPhraseId = await walletManager.importSeedPhrase(seedPhrase, password);
        console.log('Seed phrase imported with ID:', seedPhraseId);

        // Step 3: Unlock wallet for session-based operations
        console.log('\n🔓 Step 3: Unlocking wallet...');
        const unlocked = await walletManager.unlockWallet(password);
        if (!unlocked) {
            throw new Error('Failed to unlock wallet');
        }
        console.log('✅ Wallet unlocked for 30 minutes');

        // Step 4: Create wallet accounts for all 4 blockchains including Bitcoin
        console.log('\n🏦 Step 4: Creating wallet account...');
        const walletAccount = await walletManager.createWalletFromSession(
            0, // Use first seed phrase (index 0)
            'My Bitcoin Account',
            0  // Account index 0
        );

        console.log('✅ Wallet account created successfully!');
        console.log('Account name:', walletAccount.name);
        
        // Display addresses for each blockchain
        console.log('\n=== Wallet Addresses ===');
        console.log('₿ Bitcoin Address:', walletAccount.wallets.bitcoin.address);
        console.log('₿ Bitcoin Public Key:', walletAccount.wallets.bitcoin.publicKey);
        console.log('🔷 Ethereum Public Key:', walletAccount.wallets.ethereum.publicKey);
        console.log('🟣 Solana Address:', walletAccount.wallets.solana.publicKey);
        console.log('🔵 Sui Address:', walletAccount.wallets.sui.publicKey);

        // Step 5: Create additional Bitcoin accounts from the same seed phrase
        console.log('\n👥 Step 5: Creating second Bitcoin account...');
        const secondAccount = await walletManager.createWalletFromSession(
            0, // Same seed phrase
            'My Second Bitcoin Account',
            1  // Account index 1
        );

        console.log('✅ Second account created!');
        console.log('Account name:', secondAccount.name);
        console.log('₿ Bitcoin Address:', secondAccount.wallets.bitcoin.address);
        console.log('₿ Bitcoin Public Key:', secondAccount.wallets.bitcoin.publicKey);

        // Step 6: Get Bitcoin private keys using session
        console.log('\n🔑 Step 6: Getting Bitcoin private keys using session...');
        const btcPrivateKey1 = await walletManager.getPrivateKeyFromSession(0, 'bitcoin');
        const btcPrivateKey2 = await walletManager.getPrivateKeyFromSession(1, 'bitcoin');
        
        console.log('✅ Bitcoin private keys retrieved using session:');
        console.log('Account 1 Bitcoin Private Key:', btcPrivateKey1.slice(0, 10) + '...');
        console.log('Account 2 Bitcoin Private Key:', btcPrivateKey2.slice(0, 10) + '...');

        // Step 7: Sign Bitcoin data using session
        console.log('\n✍️ Step 7: Signing Bitcoin data using session...');
        const bitcoinDataToSign = 'Bitcoin transaction data to sign';
        const btcSignature1 = await walletManager.signData(0, 'bitcoin', bitcoinDataToSign);
        const btcSignature2 = await walletManager.signData(1, 'bitcoin', bitcoinDataToSign);

        console.log('✅ Bitcoin data signed using session:');
        console.log('Account 1 Bitcoin signature:', btcSignature1);
        console.log('Account 2 Bitcoin signature:', btcSignature2);

        // Step 8: Get Bitcoin addresses using helper method
        console.log('\n🏠 Step 8: Getting Bitcoin addresses...');
        const btcAddress1 = walletManager.getWalletAddress(0, 'bitcoin');
        const btcAddress2 = walletManager.getWalletAddress(1, 'bitcoin');

        console.log('Account 1 Bitcoin Address:', btcAddress1);
        console.log('Account 2 Bitcoin Address:', btcAddress2);

        // Step 9: Compare with other blockchain addresses
        console.log('\n🔗 Step 9: Multi-blockchain comparison...');
        console.log('Account 1 addresses:');
        console.log('  ₿ Bitcoin:', walletManager.getWalletAddress(0, 'bitcoin'));
        console.log('  🔷 Ethereum:', walletManager.getWalletAddress(0, 'ethereum'));
        console.log('  🟣 Solana:', walletManager.getWalletAddress(0, 'solana'));
        console.log('  🔵 Sui:', walletManager.getWalletAddress(0, 'sui'));

        // Step 10: Show wallet state
        console.log('\n📊 Step 10: Wallet state...');
        const sessionInfo = walletManager.getSessionInfo();
        console.log('Session unlocked:', sessionInfo.isUnlocked);
        console.log('Remaining minutes:', sessionInfo.remainingMinutes);
        console.log('Total seed phrases:', walletManager.getSeedPhraseCount());
        console.log('Total accounts:', walletManager.getWalletAccounts().length);

        console.log('\n🎉 Bitcoin wallet creation example completed successfully!');

        return {
            seedPhrase,
            walletAccount,
            secondAccount,
            bitcoinAddresses: [btcAddress1, btcAddress2],
            sessionInfo
        };

    } catch (error) {
        console.error('❌ Bitcoin wallet creation example failed:', error);
        throw error;
    }
}

// Example function to demonstrate Bitcoin with known seed phrase
export async function exampleBitcoinWithKnownSeed() {
    console.log('\n₿ Bitcoin with Known Seed Example\n');

    try {
        const walletManager = new WalletManager(15); // 15-minute session

        // Use a known test seed phrase (DO NOT use in production!)
        const knownSeed = 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about';
        const password = 'testBitcoinPassword123';

        console.log('Importing known seed phrase...');
        await walletManager.importSeedPhrase(knownSeed, password);
        
        console.log('Unlocking wallet...');
        await walletManager.unlockWallet(password);

        console.log('Creating Bitcoin wallet from known seed...');
        const walletAccount = await walletManager.createWalletFromSession(0, 'Known Seed Bitcoin Account', 0);

        console.log('✅ Bitcoin wallet created from known seed:');
        console.log('₿ Bitcoin Address:', walletAccount.wallets.bitcoin.address);
        console.log('₿ Bitcoin Public Key:', walletAccount.wallets.bitcoin.publicKey);
        console.log('🔷 Ethereum Public Key:', walletAccount.wallets.ethereum.publicKey);
        console.log('🟣 Solana Address:', walletAccount.wallets.solana.publicKey);
        console.log('🔵 Sui Address:', walletAccount.wallets.sui.publicKey);

        // Test multiple Bitcoin accounts from same seed
        console.log('\nCreating multiple Bitcoin accounts...');
        const accounts = [];
        for (let i = 0; i < 3; i++) {
            const account = await walletManager.createWalletFromSession(0, `Bitcoin Account ${i + 1}`, i);
            accounts.push({
                index: i,
                name: account.name,
                bitcoinAddress: account.wallets.bitcoin.address
            });
        }

        console.log('Multiple Bitcoin accounts from same seed:');
        accounts.forEach(acc => {
            console.log(`  ${acc.name}: ${acc.bitcoinAddress}`);
        });

        return { walletAccount, accounts };

    } catch (error) {
        console.error('❌ Bitcoin known seed example failed:', error);
        throw error;
    }
}

// Example function to demonstrate Bitcoin derivation paths
export async function exampleBitcoinDerivationPaths() {
    console.log('\n🛤️ Bitcoin Derivation Paths Example\n');

    try {
        const walletManager = new WalletManager(10);
        const seedPhrase = await walletManager.generateSeedPhrase();
        const password = 'derivationTestPassword';

        await walletManager.importSeedPhrase(seedPhrase, password);
        await walletManager.unlockWallet(password);

        console.log('Bitcoin uses BIP44 derivation path: m/44\'/0\'/0\'/0/{account_index}');
        console.log('Creating Bitcoin accounts with different derivation indices:\n');

        const derivationExamples = [];
        for (let i = 0; i < 5; i++) {
            const account = await walletManager.createWalletFromSession(0, `Derivation Test ${i}`, i);
            derivationExamples.push({
                accountIndex: i,
                derivationPath: `m/44'/0'/0'/0/${i}`,
                bitcoinAddress: account.wallets.bitcoin.address,
                bitcoinPublicKey: account.wallets.bitcoin.publicKey.slice(0, 20) + '...'
            });
        }

        console.log('Derivation Results:');
        derivationExamples.forEach(example => {
            console.log(`Index ${example.accountIndex} (${example.derivationPath}):`);
            console.log(`  Address: ${example.bitcoinAddress}`);
            console.log(`  Public Key: ${example.bitcoinPublicKey}\n`);
        });

        return derivationExamples;

    } catch (error) {
        console.error('❌ Bitcoin derivation paths example failed:', error);
        throw error;
    }
}

// Export all Bitcoin examples
export const bitcoinExamples = {
    exampleBitcoinWalletCreation,
    exampleBitcoinWithKnownSeed,
    exampleBitcoinDerivationPaths
};
